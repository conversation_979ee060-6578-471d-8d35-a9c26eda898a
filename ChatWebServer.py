import os
import json
import threading
import webbrowser
from http.server import <PERSON><PERSON>PServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
from ChatPersistence import ChatPersistence

class ChatWebHandler(BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.persistence = ChatPersistence()
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.serve_chat_interface()
        elif parsed_path.path == '/api/chats':
            self.handle_get_chats(parsed_path.query)
        else:
            self.send_error(404)
    
    def do_POST(self):
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/api/chats/save':
            self.handle_save_chat()
        elif parsed_path.path == '/api/chats/delete':
            self.handle_delete_chat()
        else:
            self.send_error(404)
    
    def serve_chat_interface(self):
        try:
            with open('Chat_Interface.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, "Chat_Interface.html not found")
    
    def handle_get_chats(self, query):
        try:
            params = parse_qs(query)
            model = params.get('model', ['default'])[0]
            
            chats = self.persistence.load_chats(model)
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = {'chats': chats, 'count': len(chats)}
            self.wfile.write(json.dumps(response).encode('utf-8'))
        except Exception as e:
            self.send_error(500, f"Error loading chats: {str(e)}")
    
    def handle_save_chat(self):
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            model = data.get('model', 'default')
            chat_data = data.get('chat')
            
            if not chat_data or not chat_data.get('id'):
                self.send_error(400, "Invalid chat data")
                return
            
            success = self.persistence.save_chat(model, chat_data)
            
            self.send_response(200 if success else 500)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = {'success': success}
            self.wfile.write(json.dumps(response).encode('utf-8'))
        except Exception as e:
            self.send_error(500, f"Error saving chat: {str(e)}")
    
    def handle_delete_chat(self):
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            model = data.get('model', 'default')
            chat_id = data.get('chat_id')
            
            if not chat_id:
                self.send_error(400, "Missing chat_id")
                return
            
            success = self.persistence.delete_chat(model, chat_id)
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = {'success': success}
            self.wfile.write(json.dumps(response).encode('utf-8'))
        except Exception as e:
            self.send_error(500, f"Error deleting chat: {str(e)}")
    
    def log_message(self, format, *args):
        # Suppress default logging
        pass

class ChatWebServer:
    def __init__(self, port=8765):
        self.port = port
        self.server = None
        self.server_thread = None
    
    def start(self):
        try:
            self.server = HTTPServer(('localhost', self.port), ChatWebHandler)
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            print(f"Chat web server started on http://localhost:{self.port}")
            return True
        except Exception as e:
            print(f"Failed to start chat web server: {e}")
            return False
    
    def stop(self):
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            print("Chat web server stopped")
    
    def open_browser(self):
        webbrowser.open(f'http://localhost:{self.port}')

def main():
    server = ChatWebServer()
    if server.start():
        print("Starting chat web server with persistence...")
        server.open_browser()
        try:
            input("Press Enter to stop the server...")
        except KeyboardInterrupt:
            pass
        finally:
            server.stop()
    else:
        print("Failed to start server")

if __name__ == "__main__":
    main()
