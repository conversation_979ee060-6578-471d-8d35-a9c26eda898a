<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Chat</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <style>
        :root{--p:#6366f1;--ph:#4f46e5;--sb:#f9fafb;--mb:#fff;--tp:#111827;--ts:#4b5563;--b:#e5e7eb;--ub:#f3f4f6;--f:'Inter',sans-serif}
        *{margin:0;padding:0;box-sizing:border-box}
        body{font-family:var(--f);background:var(--mb);color:var(--tp);display:flex;height:100vh;overflow:hidden}
        .sidebar{width:260px;background:var(--sb);border-right:1px solid var(--b);display:flex;flex-direction:column;height:100%}
        .sidebar-header{padding:16px;border-bottom:1px solid var(--b);display:flex;align-items:center;justify-content:space-between}
        .logo{display:flex;align-items:center;font-weight:600;font-size:18px;color:var(--tp)}
        .logo img{width:24px;height:24px;margin-right:8px}
        .new-chat-btn{width:calc(100% - 32px);padding:10px 16px;margin:16px;background:var(--p);color:#fff;border:none;border-radius:6px;font-weight:500;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:background .2s}
        .new-chat-btn:hover{background:var(--ph)}
        .new-chat-btn svg{margin-right:8px}
        .chat-history{flex:1;overflow-y:auto;padding:8px}
        .chat-item{padding:8px 12px;border-radius:6px;margin-bottom:4px;cursor:pointer;display:flex;align-items:center;color:var(--ts);font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}
        .chat-item:hover{background:rgba(0,0,0,.05)}
        .chat-item.active{background:rgba(99,102,241,.1);color:var(--p)}
        .chat-item svg{margin-right:8px;flex-shrink:0}
        .sidebar-footer{padding:16px;border-top:1px solid var(--b);font-size:14px;color:var(--ts)}

        .main-content{flex:1;display:flex;flex-direction:column;height:100%;position:relative}
        .chat-header{padding:16px;border-bottom:1px solid var(--b);display:flex;align-items:center;justify-content:space-between}
        .model-selector{display:flex;align-items:center}
        .model-selector select{padding:8px 12px;border-radius:6px;border:1px solid var(--b);background:var(--mb);font-family:var(--f);font-size:14px;color:var(--tp);margin-left:8px}
        .header-buttons{display:flex;gap:8px}
        .ollama-btn{padding:8px 12px;border-radius:6px;border:1px solid var(--b);background:var(--mb);font-family:var(--f);font-size:14px;color:var(--tp);cursor:pointer;transition:all .2s}
        .ollama-btn:hover{background:#f3f4f6}
        .check-ollama-btn{background:#e3f2fd;color:#1976d2;border-color:#bbdefb}
        .check-ollama-btn:hover{background:#bbdefb}

        .chat-container{flex:1;overflow-y:auto;padding:16px;display:flex;flex-direction:column}
        .message{display:flex;margin-bottom:24px;max-width:80%}
        .message.user-message{align-self:flex-end}
        .message.ai-message{align-self:flex-start}
        .message.system-message{align-self:center;max-width:100%;background:#fff8e1;border-radius:8px;padding:8px 16px;color:#ff8f00;font-size:14px}
        .message.error-message{align-self:center;max-width:100%;background:#ffebee;border-radius:8px;padding:8px 16px;color:#d32f2f;font-size:14px}
        .message-avatar{width:36px;height:36px;border-radius:50%;margin-right:12px;background:var(--p);display:flex;align-items:center;justify-content:center;color:#fff;font-weight:600;flex-shrink:0}
        .message-avatar.user{background:#64748b}
        .message-content{background:var(--ub);padding:12px 16px;border-radius:12px;font-size:15px;line-height:1.5}
        .ai-message .message-content{background:var(--mb);border:1px solid var(--b)}

        .input-container{padding:16px;border-top:1px solid var(--b);position:relative}
        .input-box{display:flex;border:1px solid var(--b);border-radius:12px;overflow:hidden;background:var(--mb);box-shadow:0 2px 5px rgba(0,0,0,.05)}
        #user-input{flex:1;padding:12px 16px;border:none;font-family:var(--f);font-size:15px;resize:none;min-height:24px;max-height:200px;outline:none}
        .send-button{background:var(--p);color:#fff;border:none;width:40px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:background .2s}
        .send-button:hover{background:var(--ph)}
        .send-button:disabled{background:#d1d5db;cursor:not-allowed}
        .status-bar{position:absolute;bottom:70px;left:0;right:0;text-align:center;padding:12px;font-size:14px;color:var(--ts);transition:opacity .3s;border-radius:8px;margin:0 16px;font-weight:500}
        .status-bar.error{color:#d32f2f;background:#ffebee;border:1px solid #ffcdd2}
        .status-bar.connected{color:#2e7d32;background:#e8f5e9;border:1px solid #c8e6c9}
        .status-bar.loading{color:#ed6c02;background:#fff3e0;border:1px solid #ffe0b2}

        @media (max-width:768px){.sidebar{display:none}}
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="#6366F1"/>
                    <path d="M2 17L12 22L22 17" stroke="#6366F1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M2 12L12 17L22 12" stroke="#6366F1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>DeepSeek Chat</span>
            </div>
        </div>
        <button class="new-chat-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            New Chat
        </button>
        <div class="chat-history" id="chat-history">
        </div>
        <div class="sidebar-footer">
            <div>Powered by Ollama</div>
        </div>
    </div>
    
    <div class="main-content">
        <div class="chat-header">
            <div class="model-selector">
                <label for="model-select">Model:</label>
                <select id="model-select">
                    <option value="llama2">Loading models...</option>
                </select>
            </div>
            <div class="header-buttons">
                <button id="check-ollama" class="ollama-btn check-ollama-btn">
                    Check Ollama Status
                </button>
            </div>
        </div>

        <div class="chat-container" id="chat-container"></div>

        <div class="status-bar" id="status">Checking Ollama status...</div>

        <div class="input-container">
            <div class="input-box">
                <textarea id="user-input" placeholder="Type your message here..." rows="1"></textarea>
                <button id="send-button" class="send-button">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22 2L11 13M22 2L15 22L11 13M11 13L2 9L22 2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatContainer = document.getElementById('chat-container');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');
            const modelSelect = document.getElementById('model-select');
            const checkOllamaButton = document.getElementById('check-ollama');
            const statusElement = document.getElementById('status');
            const chatHistory = document.getElementById('chat-history');
            const newChatButton = document.querySelector('.new-chat-btn');

            let ollamaRunning = false, chats = [], currentChatId = null;

            // Ensure input is always enabled and working
            if (userInput) {
                userInput.disabled = false;
                userInput.readOnly = false;
                userInput.style.pointerEvents = 'auto';
                userInput.focus();
                console.log('Input enabled:', userInput);
            }
            if (sendButton) {
                sendButton.disabled = false;
            }

            // Set up input events
            if (userInput) {
                userInput.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = this.scrollHeight + 'px';
                });
                userInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });
                // Test if input is working
                userInput.addEventListener('focus', () => console.log('Input focused'));
                userInput.addEventListener('blur', () => console.log('Input blurred'));
                userInput.addEventListener('click', () => console.log('Input clicked'));
                userInput.addEventListener('keypress', (e) => console.log('Key pressed:', e.key));

                // Force enable input after a short delay
                setTimeout(() => {
                    userInput.disabled = false;
                    userInput.readOnly = false;
                    userInput.style.pointerEvents = 'auto';
                    userInput.style.cursor = 'text';
                    console.log('Input force-enabled after delay');
                }, 100);
            }

            const startNewChat = () => {
                currentChatId = Date.now().toString();
                chats.unshift({id: currentChatId, title: 'New Chat', messages: []});
                updateChatHistory();
                chatContainer.innerHTML = '';
                addMessageToChat('System', 'How can I help you today?', 'system-message');
            };

            if (newChatButton) newChatButton.addEventListener('click', startNewChat);
            if (sendButton) sendButton.addEventListener('click', sendMessage);
            if (checkOllamaButton) checkOllamaButton.addEventListener('click', () => checkOllama(true));

            (function retryCheck(attempt = 0) {
                if (attempt < 3 && !ollamaRunning) setTimeout(() => { checkOllama(attempt === 0); retryCheck(attempt + 1); }, 1000 * (attempt + 1));
            })();

            function checkOllama(showMessage = false) {
                updateStatus('Checking Ollama...', 'loading');

                fetch('http://localhost:11434/api/tags').then(r => r.json()).then(data => {
                    ollamaRunning = true;
                    updateStatus('Connected to Ollama', 'connected');
                    populateModels(data.models);
                    if (showMessage) addMessageToChat('System', 'Connected to Ollama!', 'system-message');
                }).catch(() => {
                    fetch('http://localhost:8766/api/tags').then(r => r.json()).then(data => {
                        ollamaRunning = true;
                        updateStatus('Connected via proxy', 'connected');
                        populateModels(data.models);
                        if (showMessage) addMessageToChat('System', 'Connected via proxy!', 'system-message');
                    }).catch(() => {
                        ollamaRunning = false;
                        updateStatus('Ollama not running. Use launcher to start.', 'error');
                        if (showMessage) addMessageToChat('System', 'Ollama not running. Use the launcher to start Ollama.', 'error-message');
                    });
                });
            }

            const populateModels = models => {
                modelSelect.innerHTML = !models?.length ? '<option value="llama2">Llama 2</option><option value="mistral">Mistral</option>' :
                models.map(m => `<option value="${m.name}">${m.name}</option>`).join('');
                updateStatus(!models?.length ? 'No models found. Download models first.' : `Found ${models.length} models. Ready!`, !models?.length ? 'error' : 'connected');
            };

            const updateStatus = (message, type = '') => {
                statusElement.textContent = message;
                statusElement.className = 'status-bar' + (type ? ' ' + type : '');
            };

            function sendMessage() {
                const message = userInput.value.trim();
                if (!message) return;
                if (!currentChatId) startNewChat();

                addMessageToChat('You', message, 'user-message');
                if (chats.length && !chats[0].messages.length) {
                    chats[0].title = message.split(' ').slice(0, 4).join(' ') + '...';
                    updateChatHistory();
                }

                const chat = chats.find(c => c.id === currentChatId);
                if (chat) chat.messages.push({role: 'user', content: message});

                userInput.value = '';
                userInput.style.height = 'auto';
                updateStatus('Waiting...', 'loading');

                const body = JSON.stringify({model: modelSelect.value, prompt: message, stream: false});
                const headers = {'Content-Type': 'application/json'};

                fetch('http://localhost:11434/api/generate', {method: 'POST', headers, body})
                .then(r => r.json()).then(data => {
                    addMessageToChat('AI', data.response, 'ai-message');
                    updateStatus('Ready', 'connected');
                    if (chat) chat.messages.push({role: 'assistant', content: data.response});
                }).catch(() => {
                    fetch('http://localhost:8766/api/generate', {method: 'POST', headers, body})
                    .then(r => r.json()).then(data => {
                        addMessageToChat('AI', data.response, 'ai-message');
                        updateStatus('Ready (proxy)', 'connected');
                        if (chat) chat.messages.push({role: 'assistant', content: data.response});
                    }).catch(() => {
                        updateStatus('Error: Ollama not responding', 'error');
                        addMessageToChat('System', 'Error: Use launcher to start Ollama.', 'error-message');
                    });
                }).finally(() => {
                    userInput.focus();
                });
            }

            const addMessageToChat = (sender, message, className) => {
                const msg = document.createElement('div');
                msg.className = `message ${className}`;
                if (className.includes('system') || className.includes('error')) {
                    msg.textContent = message;
                } else {
                    msg.innerHTML = `<div class="message-avatar ${sender === 'You' ? 'user' : ''}">${sender === 'You' ? 'U' : 'A'}</div><div class="message-content">${message.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>').replace(/\n/g, '<br>')}</div>`;
                }
                chatContainer.appendChild(msg);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            };

            const updateChatHistory = () => {
                chatHistory.innerHTML = chats.map(chat => `<div class="chat-item ${chat.id === currentChatId ? 'active' : ''}" onclick="loadChat('${chat.id}')"><svg width="16" height="16" viewBox="0 0 24 24" fill="none"><path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2"/></svg>${chat.title}</div>`).join('');
            };

            window.loadChat = chatId => {
                const chat = chats.find(c => c.id === chatId);
                if (!chat) return;
                currentChatId = chatId;
                chatContainer.innerHTML = '';
                addMessageToChat('System', 'Chat loaded', 'system-message');
                chat.messages.forEach(msg => addMessageToChat(msg.role === 'user' ? 'You' : 'AI', msg.content, msg.role === 'user' ? 'user-message' : 'ai-message'));
                updateChatHistory();
            };
        });
    </script>
</body>
</html>
