<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Chat</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <style>
        :root{--p:#6366f1;--ph:#4f46e5;--sb:#f9fafb;--mb:#fff;--tp:#111827;--ts:#4b5563;--b:#e5e7eb;--ub:#f3f4f6;--f:'Inter',sans-serif}
        *{margin:0;padding:0;box-sizing:border-box}
        body{font-family:var(--f);background:var(--mb);color:var(--tp);display:flex;height:100vh;overflow:hidden}
        .sidebar{width:260px;background:var(--sb);border-right:1px solid var(--b);display:flex;flex-direction:column;height:100%}

        .new-chat-btn{width:calc(100% - 32px);padding:10px 16px;margin:16px;background:var(--p);color:#fff;border:none;border-radius:6px;font-weight:500;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:background .2s}
        .new-chat-btn:hover{background:var(--ph)}
        .new-chat-btn svg{margin-right:8px}
        .chat-history{flex:1;overflow-y:auto;padding:16px 8px}
        .chat-item{padding:8px 12px;border-radius:6px;margin-bottom:4px;cursor:pointer;display:flex;align-items:center;color:var(--ts);font-size:14px;position:relative}
        .chat-item:hover{background:rgba(0,0,0,.05)}
        .chat-item.active{background:rgba(99,102,241,.1);color:var(--p)}
        .chat-item svg{margin-right:8px;flex-shrink:0}
        .chat-title{flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}
        .delete-chat-btn{background:none;border:none;color:var(--ts);cursor:pointer;padding:2px 6px;border-radius:3px;font-size:16px;line-height:1;opacity:0;transition:opacity .2s;margin-left:4px}
        .chat-item:hover .delete-chat-btn{opacity:1}
        .delete-chat-btn:hover{background:rgba(255,0,0,0.1);color:#ff4444}


        .main-content{flex:1;display:flex;flex-direction:column;height:100%;position:relative}
        .chat-header{padding:16px;border-bottom:1px solid var(--b);display:flex;align-items:center}
        .model-selector{display:flex;align-items:center}
        .model-selector select{padding:8px 12px;border-radius:6px;border:1px solid var(--b);background:var(--mb);font-family:var(--f);font-size:14px;color:var(--tp);margin-left:8px}


        .chat-container{flex:1;overflow-y:auto;padding:16px;display:flex;flex-direction:column}
        .message{display:flex;margin-bottom:24px;max-width:80%}
        .message.user-message{align-self:flex-end}
        .message.ai-message{align-self:flex-start}
        .message.system-message{align-self:center;max-width:100%;background:#fff8e1;border-radius:8px;padding:8px 16px;color:#ff8f00;font-size:14px}
        .message.error-message{align-self:center;max-width:100%;background:#ffebee;border-radius:8px;padding:8px 16px;color:#d32f2f;font-size:14px}
        .message-avatar{width:36px;height:36px;border-radius:50%;margin-right:12px;background:var(--p);display:flex;align-items:center;justify-content:center;color:#fff;font-weight:600;flex-shrink:0}
        .message-avatar.user{background:#64748b}
        .message-content{background:var(--ub);padding:12px 16px;border-radius:12px;font-size:15px;line-height:1.5;word-wrap:break-word;max-width:100%}
        .ai-message .message-content{background:var(--mb);border:1px solid var(--b)}

        .input-container{padding:16px;border-top:1px solid var(--b);position:relative}
        .input-box{display:flex;border:1px solid var(--b);border-radius:12px;overflow:hidden;background:var(--mb);box-shadow:0 2px 5px rgba(0,0,0,.05)}
        #user-input{flex:1;padding:12px 16px;border:none;font-family:var(--f);font-size:15px;resize:none;min-height:24px;max-height:200px;outline:none}
        .send-button{background:var(--p);color:#fff;border:none;width:40px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:background .2s}
        .send-button:hover{background:var(--ph)}
        .send-button:disabled{background:#d1d5db;cursor:not-allowed}
        .status-bar{position:absolute;bottom:70px;left:0;right:0;text-align:center;padding:12px;font-size:14px;color:var(--ts);transition:opacity .3s;border-radius:8px;margin:0 16px;font-weight:500}
        .status-bar.error{color:#d32f2f;background:#ffebee;border:1px solid #ffcdd2}
        .status-bar.connected{color:#2e7d32;background:#e8f5e9;border:1px solid #c8e6c9}
        .status-bar.loading{color:#ed6c02;background:#fff3e0;border:1px solid #ffe0b2}

        @media (max-width:768px){.sidebar{display:none}}
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="chat-history" id="chat-history">
        </div>
        <button class="new-chat-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            New Chat
        </button>
    </div>
    
    <div class="main-content">
        <div class="chat-header">
            <div class="model-selector">
                <label for="model-select">Model:</label>
                <select id="model-select">
                    <option value="llama3.2">Llama 3.2</option>
                    <option value="llama3.1">Llama 3.1</option>
                    <option value="llama2">Llama 2</option>
                    <option value="mistral">Mistral</option>
                    <option value="codellama">Code Llama</option>
                    <option value="deepseek-coder">DeepSeek Coder</option>
                </select>
            </div>

        </div>

        <div class="chat-container" id="chat-container"></div>

        <div class="status-bar" id="status" style="display: none;">Ready</div>

        <div class="input-container">
            <div class="input-box">
                <textarea id="user-input" placeholder="Type your message here..." rows="1"></textarea>
                <button id="send-button" class="send-button">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22 2L11 13M22 2L15 22L11 13M11 13L2 9L22 2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatContainer = document.getElementById('chat-container');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');
            const modelSelect = document.getElementById('model-select');
            const statusElement = document.getElementById('status');
            const chatHistory = document.getElementById('chat-history');
            const newChatButton = document.querySelector('.new-chat-btn');

            let ollamaRunning = false, chats = [], currentChatId = null;

            // Chat persistence functions
            const getChatFilePath = (model, chatId) => `previous_chats/${model}/${chatId}.json`;

            const saveChat = async (chat) => {
                if (!chat || !chat.id || !chat.messages.length) return;
                const model = modelSelect.value || 'default';
                const chatData = {
                    id: chat.id,
                    title: chat.title,
                    model: model,
                    messages: chat.messages,
                    timestamp: Date.now()
                };

                try {
                    const response = await fetch('/api/chats/save', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({model: model, chat: chatData})
                    });

                    if (response.ok) {
                        console.log(`Chat saved: ${chat.id} for model: ${model}`);
                    } else {
                        console.error('Failed to save chat:', response.statusText);
                        // Fallback to localStorage
                        const key = `chat_${model}_${chat.id}`;
                        localStorage.setItem(key, JSON.stringify(chatData));
                    }
                } catch (e) {
                    console.error('Failed to save chat:', e);
                    // Fallback to localStorage
                    try {
                        const key = `chat_${model}_${chat.id}`;
                        localStorage.setItem(key, JSON.stringify(chatData));
                    } catch (localError) {
                        console.error('LocalStorage fallback failed:', localError);
                    }
                }
            };

            const loadChats = async () => {
                const currentModel = modelSelect.value || 'default';

                try {
                    const response = await fetch(`/api/chats?model=${encodeURIComponent(currentModel)}`);

                    if (response.ok) {
                        const data = await response.json();
                        chats = data.chats || [];
                        updateChatHistory();
                        console.log(`Loaded ${chats.length} chats for model: ${currentModel}`);
                    } else {
                        console.error('Failed to load chats from server, trying localStorage...');
                        loadChatsFromLocalStorage(currentModel);
                    }
                } catch (e) {
                    console.error('Failed to load chats from server:', e);
                    loadChatsFromLocalStorage(currentModel);
                }
            };

            const loadChatsFromLocalStorage = (currentModel) => {
                const loadedChats = [];
                try {
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key && key.startsWith(`chat_${currentModel}_`)) {
                            const chatData = JSON.parse(localStorage.getItem(key));
                            if (chatData && chatData.messages && chatData.messages.length > 0) {
                                loadedChats.push(chatData);
                            }
                        }
                    }
                    loadedChats.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
                    chats = loadedChats;
                    updateChatHistory();
                    console.log(`Loaded ${loadedChats.length} chats from localStorage for model: ${currentModel}`);
                } catch (e) {
                    console.error('Failed to load chats from localStorage:', e);
                }
            };

            const deleteChat = async (chatId) => {
                const model = modelSelect.value || 'default';

                try {
                    const response = await fetch('/api/chats/delete', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({model: model, chat_id: chatId})
                    });

                    if (response.ok) {
                        chats = chats.filter(c => c.id !== chatId);
                        updateChatHistory();
                        console.log(`Chat deleted: ${chatId} for model: ${model}`);

                        // If we deleted the current chat, clear the container
                        if (currentChatId === chatId) {
                            currentChatId = null;
                            chatContainer.innerHTML = '';
                        }
                    } else {
                        console.error('Failed to delete chat from server');
                    }
                } catch (e) {
                    console.error('Failed to delete chat:', e);
                    // Fallback to localStorage
                    try {
                        const key = `chat_${model}_${chatId}`;
                        localStorage.removeItem(key);
                        chats = chats.filter(c => c.id !== chatId);
                        updateChatHistory();
                    } catch (localError) {
                        console.error('LocalStorage fallback failed:', localError);
                    }
                }
            };

            // Ensure input is always enabled and working
            if (userInput) {
                userInput.disabled = false;
                userInput.readOnly = false;
                userInput.style.pointerEvents = 'auto';
                userInput.focus();
                console.log('Input enabled:', userInput);
            }
            if (sendButton) {
                sendButton.disabled = false;
            }

            // Set up input events
            if (userInput) {
                userInput.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = this.scrollHeight + 'px';
                });
                userInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });
                // Test if input is working
                userInput.addEventListener('focus', () => console.log('Input focused'));
                userInput.addEventListener('blur', () => console.log('Input blurred'));
                userInput.addEventListener('click', () => console.log('Input clicked'));
                userInput.addEventListener('keypress', (e) => console.log('Key pressed:', e.key));

                // Force enable input after a short delay
                setTimeout(() => {
                    userInput.disabled = false;
                    userInput.readOnly = false;
                    userInput.style.pointerEvents = 'auto';
                    userInput.style.cursor = 'text';
                    console.log('Input force-enabled after delay');
                }, 100);
            }

            const startNewChat = () => {
                currentChatId = Date.now().toString();
                const newChat = {
                    id: currentChatId,
                    title: 'New Chat',
                    messages: [],
                    model: modelSelect.value || 'default',
                    timestamp: Date.now()
                };
                chats.unshift(newChat);
                updateChatHistory();
                chatContainer.innerHTML = '';
                addMessageToChat('System', 'How can I help you today?', 'system-message');
            };

            if (newChatButton) newChatButton.addEventListener('click', startNewChat);
            if (sendButton) sendButton.addEventListener('click', sendMessage);
            if (modelSelect) modelSelect.addEventListener('change', () => {
                // Save current chat before switching models
                if (currentChatId) {
                    const currentChat = chats.find(c => c.id === currentChatId);
                    if (currentChat && currentChat.messages.length > 0) {
                        saveChat(currentChat);
                    }
                }
                // Load chats for new model
                loadChats();
                currentChatId = null;
                chatContainer.innerHTML = '';
            });

            // Check Ollama once on load
            checkOllama(false);

            function checkOllama(showMessage = false) {
                // Try direct connection first
                fetch('http://localhost:11434/api/tags', {signal: AbortSignal.timeout(3000)})
                .then(r => r.json()).then(data => {
                    ollamaRunning = true;
                    populateModels(data.models);
                    console.log('Connected to Ollama directly');
                }).catch(() => {
                    // Try proxy connection
                    fetch('http://localhost:8766/api/tags', {signal: AbortSignal.timeout(3000)})
                    .then(r => r.json()).then(data => {
                        ollamaRunning = true;
                        populateModels(data.models);
                        console.log('Connected via proxy');
                    }).catch(() => {
                        ollamaRunning = false;
                        // Load default models if Ollama not available
                        populateModels([]);
                        console.log('Ollama not available, using defaults');
                    });
                });
            }

            const populateModels = models => {
                if (!models?.length) {
                    // Default models when Ollama is not available
                    modelSelect.innerHTML = `
                        <option value="llama3.2">Llama 3.2</option>
                        <option value="llama3.1">Llama 3.1</option>
                        <option value="llama2">Llama 2</option>
                        <option value="mistral">Mistral</option>
                        <option value="codellama">Code Llama</option>
                        <option value="deepseek-coder">DeepSeek Coder</option>
                    `;
                } else {
                    // Use actual models from Ollama
                    modelSelect.innerHTML = models.map(m => `<option value="${m.name}">${m.name}</option>`).join('');
                }
                // Load chats for current model after populating models
                loadChats();
            };

            const updateStatus = (message, type = '') => {
                // Only show status for errors or important messages
                if (type === 'error') {
                    statusElement.textContent = message;
                    statusElement.className = 'status-bar ' + type;
                    statusElement.style.display = 'block';
                    setTimeout(() => statusElement.style.display = 'none', 5000);
                } else {
                    statusElement.style.display = 'none';
                }
            };

            function sendMessage() {
                const message = userInput.value.trim();
                if (!message) return;
                if (!currentChatId) startNewChat();

                addMessageToChat('You', message, 'user-message');
                if (chats.length && !chats[0].messages.length) {
                    chats[0].title = message.split(' ').slice(0, 4).join(' ') + '...';
                    updateChatHistory();
                }

                const chat = chats.find(c => c.id === currentChatId);
                if (chat) chat.messages.push({role: 'user', content: message});

                userInput.value = '';
                userInput.style.height = 'auto';

                const body = JSON.stringify({model: modelSelect.value, prompt: message, stream: false});
                const headers = {'Content-Type': 'application/json'};

                fetch('http://localhost:11434/api/generate', {method: 'POST', headers, body})
                .then(r => r.json()).then(data => {
                    addMessageToChat('AI', data.response, 'ai-message');
                    if (chat) {
                        chat.messages.push({role: 'assistant', content: data.response});
                        saveChat(chat);
                    }
                }).catch(() => {
                    fetch('http://localhost:8766/api/generate', {method: 'POST', headers, body})
                    .then(r => r.json()).then(data => {
                        addMessageToChat('AI', data.response, 'ai-message');
                        if (chat) {
                            chat.messages.push({role: 'assistant', content: data.response});
                            saveChat(chat);
                        }
                    }).catch(() => {
                        updateStatus('Ollama not available. Use the launcher to start Ollama.', 'error');
                        addMessageToChat('System', 'Ollama not available. Use the launcher to start Ollama and download models.', 'error-message');
                    });
                }).finally(() => {
                    userInput.focus();
                });
            }

            const addMessageToChat = (sender, message, className) => {
                const msg = document.createElement('div');
                msg.className = `message ${className}`;
                if (className.includes('system') || className.includes('error')) {
                    msg.textContent = message;
                } else {
                    msg.innerHTML = `<div class="message-avatar ${sender === 'You' ? 'user' : ''}">${sender === 'You' ? 'U' : 'A'}</div><div class="message-content">${message.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>').replace(/\n/g, '<br>')}</div>`;
                }
                chatContainer.appendChild(msg);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            };

            const updateChatHistory = () => {
                chatHistory.innerHTML = chats.map(chat => `
                    <div class="chat-item ${chat.id === currentChatId ? 'active' : ''}" onclick="loadChat('${chat.id}')">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        <span class="chat-title">${chat.title}</span>
                        <button class="delete-chat-btn" onclick="event.stopPropagation(); deleteChat('${chat.id}')" title="Delete chat">×</button>
                    </div>
                `).join('');
            };

            window.loadChat = chatId => {
                const chat = chats.find(c => c.id === chatId);
                if (!chat) return;
                currentChatId = chatId;
                chatContainer.innerHTML = '';
                addMessageToChat('System', 'Chat loaded', 'system-message');
                chat.messages.forEach(msg => addMessageToChat(msg.role === 'user' ? 'You' : 'AI', msg.content, msg.role === 'user' ? 'user-message' : 'ai-message'));
                updateChatHistory();
            };
        });
    </script>
</body>
</html>
