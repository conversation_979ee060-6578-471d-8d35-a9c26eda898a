"""
OllamaInstaller Module
Handles the installation of Ollama with GUI integration
"""

import subprocess
import threading
import webbrowser
from tkinter import messagebox

try:
    from Ollama import OllamaManager
except ImportError:
    print("Ollama manager module not found, functionality will be limited")
    
    class OllamaManager:
        @staticmethod
        def install_ollama():
            print("Ollama manager module not found, cannot install Ollama")
            return False


class OllamaInstaller:
    """Handles Ollama installation functionality"""
    
    def __init__(self, status_callback=None, root=None):
        """
        Initialize the OllamaInstaller
        
        Args:
            status_callback: Function to update status messages
            root: Tkinter root window for thread-safe GUI updates
        """
        self.status_callback = status_callback
        self.root = root
    
    def check_ollama_installed(self):
        """
        Check if Ollama is already installed
        
        Returns:
            bool: True if Ollama is installed, False otherwise
        """
        try:
            result = subprocess.run(['ollama', 'list'], 
                                  capture_output=True, 
                                  text=True, 
                                  encoding='utf-8')
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def install_ollama(self):
        """
        Install Ollama with GUI feedback
        """
        # Check if already installed
        if self.check_ollama_installed():
            messagebox.showinfo("Ollama", "Ollama is already installed.")
            return
        
        # Update status
        if self.status_callback:
            self.status_callback("Installing Ollama...")
        
        def install_thread():
            """Background thread for installation"""
            success = OllamaManager.install_ollama()
            
            if self.root:
                if success:
                    self.root.after(0, lambda: self._on_install_success())
                else:
                    self.root.after(0, lambda: self._on_install_failure())
            else:
                if success:
                    self._on_install_success()
                else:
                    self._on_install_failure()
        
        # Start installation in background thread
        threading.Thread(target=install_thread, daemon=True).start()
    
    def _on_install_success(self):
        """Handle successful installation"""
        if self.status_callback:
            self.status_callback("Ollama installed successfully")
        messagebox.showinfo("Ollama", "Ollama has been installed successfully.")
    
    def _on_install_failure(self):
        """Handle installation failure"""
        # Open download page as fallback
        webbrowser.open("https://ollama.com/download/windows")
        messagebox.showinfo("Install Ollama",
                          "Please download and install Ollama from the website that just opened.\n\n"
                          "After installation is complete, you may need to restart your computer.")
    
    @staticmethod
    def get_installation_info():
        """
        Get information about Ollama installation
        
        Returns:
            dict: Installation information
        """
        installer = OllamaInstaller()
        is_installed = installer.check_ollama_installed()
        
        return {
            'installed': is_installed,
            'download_url': 'https://ollama.com/download/windows',
            'name': 'Ollama',
            'description': 'Local AI model runtime'
        }


def main():
    """Main function for testing the module"""
    installer = OllamaInstaller()
    
    print("Checking Ollama installation...")
    if installer.check_ollama_installed():
        print("✓ Ollama is already installed")
    else:
        print("✗ Ollama is not installed")
        print("Run installer.install_ollama() to install")


if __name__ == "__main__":
    main()
