"""
OllamaStarter Module
Handles starting Ollama service with GUI integration
"""

import subprocess
import threading
import time
import requests
from tkinter import messagebox

try:
    from Ollama import OllamaManager
except ImportError:
    print("Ollama manager module not found, functionality will be limited")
    
    class OllamaManager:
        @staticmethod
        def start_ollama():
            print("Ollama manager module not found, cannot start Ollama")
            return False
        
        @staticmethod
        def check_ollama_running():
            return False


class OllamaStarter:
    """Handles Ollama service starting functionality"""
    
    def __init__(self, status_callback=None, root=None):
        """
        Initialize the OllamaStarter
        
        Args:
            status_callback: Function to update status messages
            root: Tkinter root window for thread-safe GUI updates
        """
        self.status_callback = status_callback
        self.root = root
    
    def check_ollama_installed(self):
        """
        Check if Ollama is installed
        
        Returns:
            bool: True if Ollama is installed, False otherwise
        """
        try:
            result = subprocess.run(['ollama', 'list'], 
                                  capture_output=True, 
                                  text=True, 
                                  encoding='utf-8')
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def check_ollama_running(self):
        """
        Check if <PERSON>llama is currently running
        
        Returns:
            bool: True if Ollama is running, False otherwise
        """
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            return response.status_code == 200
        except:
            return False
    
    def start_ollama(self):
        """
        Start Ollama service with GUI feedback
        """
        # Check if already running
        if self.check_ollama_running():
            messagebox.showinfo("Ollama", "Ollama is already running.")
            return
        
        # Check if installed
        if not self.check_ollama_installed():
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return
        
        # Update status
        if self.status_callback:
            self.status_callback("Starting Ollama...")
        
        def start_ollama_thread():
            """Background thread for starting Ollama"""
            try:
                success = OllamaManager.start_ollama()
                
                if success:
                    if self.root:
                        self.root.after(0, lambda: self._on_start_success())
                    else:
                        self._on_start_success()
                else:
                    # Fallback to direct command
                    if self.root:
                        self.root.after(0, lambda: self._try_fallback_start())
                    else:
                        self._try_fallback_start()
                        
            except Exception as e:
                if self.root:
                    self.root.after(0, lambda: self._on_start_error(str(e)))
                else:
                    self._on_start_error(str(e))
        
        # Start in background thread
        threading.Thread(target=start_ollama_thread, daemon=True).start()
    
    def _try_fallback_start(self):
        """Try fallback method to start Ollama"""
        try:
            subprocess.Popen(['ollama', 'serve'],
                           stdout=subprocess.PIPE,
                           stderr=subprocess.PIPE,
                           creationflags=subprocess.CREATE_NO_WINDOW)
            
            # Wait for Ollama to start
            for _ in range(10):  # Try for 10 seconds
                try:
                    response = requests.get("http://localhost:11434/api/tags", timeout=1)
                    if response.status_code == 200:
                        self._on_start_success()
                        return
                except:
                    pass
                time.sleep(1)
            
            self._on_start_warning()
            
        except Exception as e:
            self._on_start_error(str(e))
    
    def _on_start_success(self):
        """Handle successful start"""
        if self.status_callback:
            self.status_callback("Ollama started successfully")
        messagebox.showinfo("Ollama", "Ollama started successfully.")
    
    def _on_start_warning(self):
        """Handle start warning"""
        if self.status_callback:
            self.status_callback("Failed to start Ollama")
        messagebox.showwarning("Ollama", 
                             "Ollama may not have started properly. "
                             "Please check if Ollama is running in the background.")
    
    def _on_start_error(self, error_msg):
        """Handle start error"""
        if self.status_callback:
            self.status_callback("Error starting Ollama")
        messagebox.showerror("Error", f"Failed to start Ollama: {error_msg}")
    
    @staticmethod
    def get_service_status():
        """
        Get current status of Ollama service
        
        Returns:
            dict: Service status information
        """
        starter = OllamaStarter()
        
        return {
            'installed': starter.check_ollama_installed(),
            'running': starter.check_ollama_running(),
            'service_name': 'Ollama',
            'port': 11434,
            'api_endpoint': 'http://localhost:11434/api/tags'
        }


def main():
    """Main function for testing the module"""
    starter = OllamaStarter()
    
    print("Checking Ollama status...")
    status = starter.get_service_status()
    
    print(f"Installed: {status['installed']}")
    print(f"Running: {status['running']}")
    
    if status['installed'] and not status['running']:
        print("Ollama is installed but not running.")
        print("Run starter.start_ollama() to start it")


if __name__ == "__main__":
    main()
