import os
import json
import time
from pathlib import Path

class ChatPersistence:
    def __init__(self, base_dir="previous_chats"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
    
    def get_model_dir(self, model):
        model_dir = self.base_dir / model.replace(":", "_").replace("/", "_")
        model_dir.mkdir(exist_ok=True)
        return model_dir
    
    def save_chat(self, model, chat_data):
        try:
            model_dir = self.get_model_dir(model)
            chat_file = model_dir / f"{chat_data['id']}.json"
            
            # Add timestamp if not present
            if 'timestamp' not in chat_data:
                chat_data['timestamp'] = int(time.time() * 1000)
            
            with open(chat_file, 'w', encoding='utf-8') as f:
                json.dump(chat_data, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"Error saving chat: {e}")
            return False
    
    def load_chats(self, model):
        try:
            model_dir = self.get_model_dir(model)
            chats = []
            
            for chat_file in model_dir.glob("*.json"):
                try:
                    with open(chat_file, 'r', encoding='utf-8') as f:
                        chat_data = json.load(f)
                        if chat_data.get('messages'):  # Only load chats with messages
                            chats.append(chat_data)
                except Exception as e:
                    print(f"Error loading chat {chat_file}: {e}")
            
            # Sort by timestamp (newest first)
            chats.sort(key=lambda x: x.get('timestamp', 0), reverse=True)
            return chats
        except Exception as e:
            print(f"Error loading chats for model {model}: {e}")
            return []
    
    def delete_chat(self, model, chat_id):
        try:
            model_dir = self.get_model_dir(model)
            chat_file = model_dir / f"{chat_id}.json"
            
            if chat_file.exists():
                chat_file.unlink()
                return True
            return False
        except Exception as e:
            print(f"Error deleting chat: {e}")
            return False
    
    def get_chat_count(self, model):
        try:
            model_dir = self.get_model_dir(model)
            return len(list(model_dir.glob("*.json")))
        except:
            return 0
    
    def cleanup_old_chats(self, model, max_chats=50):
        """Keep only the most recent max_chats for a model"""
        try:
            chats = self.load_chats(model)
            if len(chats) > max_chats:
                # Delete oldest chats
                for chat in chats[max_chats:]:
                    self.delete_chat(model, chat['id'])
                return len(chats) - max_chats
            return 0
        except Exception as e:
            print(f"Error cleaning up chats: {e}")
            return 0

# Test the persistence system
if __name__ == "__main__":
    persistence = ChatPersistence()
    
    # Test data
    test_chat = {
        "id": "test_123",
        "title": "Test Chat",
        "model": "llama3.2",
        "messages": [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there!"}
        ]
    }
    
    # Test save
    print("Testing save...")
    success = persistence.save_chat("llama3.2", test_chat)
    print(f"Save result: {success}")
    
    # Test load
    print("Testing load...")
    chats = persistence.load_chats("llama3.2")
    print(f"Loaded {len(chats)} chats")
    
    # Test delete
    print("Testing delete...")
    deleted = persistence.delete_chat("llama3.2", "test_123")
    print(f"Delete result: {deleted}")
    
    print("Chat persistence system ready!")
