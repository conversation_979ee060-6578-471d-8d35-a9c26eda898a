"""
GUILauncher <PERSON><PERSON><PERSON>
Handles launching the DeepSeek GUI interface
"""

import subprocess
import threading
from tkinter import messagebox


class GUILauncher:
    """Handles GUI launching functionality"""
    
    def __init__(self, status_callback=None, root=None):
        """
        Initialize the GUILauncher
        
        Args:
            status_callback: Function to update status messages
            root: Tkinter root window for thread-safe GUI updates
        """
        self.status_callback = status_callback
        self.root = root
    
    def check_gui_available(self):
        """
        Check if GUI.py is available
        
        Returns:
            bool: True if GUI.py exists and can be imported
        """
        try:
            import GUI
            return True
        except ImportError:
            return False
    
    def launch_gui(self):
        """Launch the DeepSeek GUI interface"""
        if self.status_callback:
            self.status_callback("Launching DeepSeek GUI...")
        
        # Check if GUI module is available
        if not self.check_gui_available():
            messagebox.showerror("Error", 
                               "GUI interface module not found. Please ensure GUI.py is available.")
            if self.status_callback:
                self.status_callback("GUI module not found")
            return
        
        def launch_thread():
            """Background thread for launching GUI"""
            try:
                # Import and launch GUI
                import GUI
                
                if self.root:
                    self.root.after(0, lambda: self._on_launch_success())
                else:
                    self._on_launch_success()
                    
                # Start the GUI (this will block until GUI is closed)
                GUI.main()
                
            except Exception as e:
                if self.root:
                    self.root.after(0, lambda: self._on_launch_error(str(e)))
                else:
                    self._on_launch_error(str(e))
        
        # Start GUI in background thread
        threading.Thread(target=launch_thread, daemon=True).start()
    
    def launch_gui_subprocess(self):
        """Launch GUI as a separate subprocess"""
        if self.status_callback:
            self.status_callback("Launching DeepSeek GUI in new window...")
        
        try:
            # Launch GUI.py as a separate process
            subprocess.Popen(['python', 'GUI.py'], 
                           stdout=subprocess.PIPE,
                           stderr=subprocess.PIPE)
            
            if self.status_callback:
                self.status_callback("GUI launched successfully")
            messagebox.showinfo("GUI Launched", 
                              "DeepSeek GUI has been launched in a new window.")
            
        except FileNotFoundError:
            messagebox.showerror("Error", 
                               "GUI.py not found. Please ensure GUI.py is in the current directory.")
            if self.status_callback:
                self.status_callback("GUI.py not found")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch GUI: {str(e)}")
            if self.status_callback:
                self.status_callback("Failed to launch GUI")
    
    def _on_launch_success(self):
        """Handle successful GUI launch"""
        if self.status_callback:
            self.status_callback("GUI launched successfully")
        messagebox.showinfo("GUI Launched", 
                          "DeepSeek GUI interface has been launched.")
    
    def _on_launch_error(self, error_msg):
        """Handle GUI launch error"""
        if self.status_callback:
            self.status_callback("Failed to launch GUI")
        messagebox.showerror("Error", f"Failed to launch GUI: {error_msg}")
    
    @staticmethod
    def get_gui_status():
        """
        Get GUI availability status
        
        Returns:
            dict: GUI status information
        """
        launcher = GUILauncher()
        
        return {
            'available': launcher.check_gui_available(),
            'module_name': 'GUI.py',
            'description': 'DeepSeek GUI Interface'
        }


def main():
    """Main function for testing the module"""
    launcher = GUILauncher()
    
    print("Checking GUI availability...")
    status = launcher.get_gui_status()
    
    print(f"GUI available: {status['available']}")
    print(f"Module: {status['module_name']}")
    print(f"Description: {status['description']}")
    
    if status['available']:
        print("GUI can be launched with launcher.launch_gui()")
    else:
        print("GUI module not found")


if __name__ == "__main__":
    main()
