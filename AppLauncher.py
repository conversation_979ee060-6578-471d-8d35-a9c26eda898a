
import os
import sys
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox
import requests

# Import the new modular components
try:
    from OllamaInstaller import OllamaInstaller
    from OllamaStarter import OllamaStarter
    from ModelDownloader import ModelDownloader
    from ModelStatusChecker import ModelStatusChecker
    from SystemInfo import SystemInfo
    from GUILauncher import GUILauncher
    from WebInterfaceLauncher import WebInterfaceLauncher
    from Ollama import start_api_server_in_background, OllamaManager
except ImportError as e:
    print(f"Warning: Some modules not found: {e}")
    print("Some functionality may be limited")

    # Fallback classes for missing modules
    class OllamaInstaller:
        def __init__(self, *args, **kwargs): pass
        def install_ollama(self):
            messagebox.showerror("Error", "OllamaInstaller module not found")

    class OllamaStarter:
        def __init__(self, *args, **kwargs): pass
        def start_ollama(self):
            messagebox.showerror("Error", "OllamaStarter module not found")

    class ModelDownloader:
        def __init__(self, *args, **kwargs): pass
        def download_model(self):
            messagebox.showerror("Error", "ModelDownloader module not found")

    class ModelStatusChecker:
        def __init__(self, *args, **kwargs): pass
        def check_model_status(self):
            messagebox.showerror("Error", "ModelStatusChecker module not found")

    class SystemInfo:
        def __init__(self, *args, **kwargs): pass
        def show_system_info(self):
            messagebox.showerror("Error", "SystemInfo module not found")

    class GUILauncher:
        def __init__(self, *args, **kwargs): pass
        def launch_gui(self):
            messagebox.showerror("Error", "GUILauncher module not found")

    class WebInterfaceLauncher:
        def __init__(self, *args, **kwargs): pass
        def open_web_interface(self):
            messagebox.showerror("Error", "WebInterfaceLauncher module not found")

    def start_api_server_in_background(port=8765):
        print("Ollama API server module not found, skipping")
        return None

    class OllamaManager:
        @staticmethod
        def check_ollama_running():
            return False

        @staticmethod
        def start_ollama():
            return False

def check_and_install_dependencies():
    print("Checking dependencies...")

    required_packages = [
        "requests",
        "psutil",
        "tkinter"
    ]

    missing_packages = []
    for package in required_packages:
        try:
            if package == "tkinter":
                __import__(package)
                print(f"✓ {package} is installed")
            else:
                __import__(package)
                print(f"✓ {package} is installed")
        except ImportError:
            print(f"✗ {package} is not installed")
            missing_packages.append(package)

    if missing_packages:
        print("\nInstalling missing packages...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing_packages)
            print("All dependencies installed successfully!")
        except subprocess.CalledProcessError as e:
            print(f"Error installing dependencies: {e}")
            print("\nPlease try to install them manually using:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    else:
        print("\nAll dependencies are already installed!")

    print("\nChecking if Ollama is installed...")
    try:
        result = subprocess.run(['ollama', 'list'],
                              capture_output=True,
                              text=True,
                              encoding='utf-8')
        if result.returncode == 0:
            print("✓ Ollama is installed")
        else:
            print("✗ Ollama is not installed or not working properly")
            print("Please install Ollama from: https://ollama.com/download/windows")
            return False
    except FileNotFoundError:
        print("✗ Ollama is not installed or not in PATH")
        print("Please install Ollama from: https://ollama.com/download/windows")
        return False

    print("\nDependency check completed!")
    return True

def start_ollama():
    print("\nChecking if Ollama is running...")

    if OllamaManager.check_ollama_running():
        print("✓ Ollama is already running")
        return True

    print("Starting Ollama...")
    if OllamaManager.start_ollama():
        print("✓ Ollama started successfully")
        return True
    else:
        print("✗ Failed to start Ollama")
        return False

def start_api_server():
    print("\nStarting API server...")

    api_thread = start_api_server_in_background()
    if api_thread:
        print("✓ API server started successfully")
        return True
    else:
        print("✗ API server may already be running")
        return True

class DeepSeekLauncher:

    def __init__(self, root):
        self.root = root
        self.root.title("Chat Launcher")
        self.root.geometry("700x550")
        self.root.minsize(700, 550)

        # Initialize modular components
        self.ollama_installer = OllamaInstaller(self.update_status, root)
        self.ollama_starter = OllamaStarter(self.update_status, root)
        self.model_downloader = ModelDownloader(self.update_status, root)
        self.model_status_checker = ModelStatusChecker(self.update_status, root)
        self.system_info = SystemInfo(self.update_status, root)
        self.gui_launcher = GUILauncher(self.update_status, root)
        self.web_interface_launcher = WebInterfaceLauncher(self.update_status, root)

        header_frame = ttk.Frame(root, padding=15)
        header_frame.pack(fill=tk.X)

        title_label = ttk.Label(header_frame, text="Chat Launcher", font=("Arial", 20, "bold"))
        title_label.pack(pady=(0, 5))

        separator = ttk.Separator(root, orient='horizontal')
        separator.pack(fill=tk.X, padx=15, pady=5)

        main_frame = ttk.Frame(root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        buttons_frame.columnconfigure(0, weight=1, uniform='column')
        buttons_frame.columnconfigure(1, weight=1, uniform='column')

        for i in range(4):
            buttons_frame.rowconfigure(i, minsize=50)

        self.create_button(buttons_frame, "Install Ollama", self.install_ollama, 0, 0)
        self.create_button(buttons_frame, "Start Ollama", self.start_ollama, 0, 1)

        self.create_button(buttons_frame, "Download AI Model", self.download_model, 1, 0)
        self.create_button(buttons_frame, "Check Model Status", self.check_model_status, 1, 1)

        self.create_button(buttons_frame, "System Information", self.system_info, 2, 0, columnspan=2)

        self.create_button(buttons_frame, "Run DeepSeek GUI", self.run_gui, 3, 0)
        self.create_button(buttons_frame, "Open Web Interface", self.open_web_interface, 3, 1)

        status_frame = ttk.Frame(root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)

        ttk.Separator(status_frame, orient='horizontal').pack(fill=tk.X, pady=(5, 2))

        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W, padding=(10, 5))
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

        self.check_ollama_status()

    def create_button(self, parent, text, command, row, column, columnspan=1):
        button = ttk.Button(parent, text=text, command=command, width=30)
        button.grid(row=row, column=column, padx=15, pady=10, sticky="nsew", columnspan=columnspan)
        return button

    def update_status(self, message):
        """Update the status bar with a message"""
        self.status_var.set(message)

    def check_ollama_status(self):
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                self.status_var.set("Ollama is installed")

                try:
                    response = requests.get("http://localhost:11434/api/tags", timeout=2)
                    if response.status_code == 200:
                        self.status_var.set("Ollama is installed and running")
                except:
                    self.status_var.set("Ollama is installed but not running")
            else:
                self.status_var.set("Ollama is not installed or not in PATH")
        except FileNotFoundError:
            self.status_var.set("Ollama is not installed or not in PATH")

    def install_ollama(self):
        """Install Ollama using the OllamaInstaller module"""
        self.ollama_installer.install_ollama()

    def start_ollama(self):
        """Start Ollama using the OllamaStarter module"""
        self.ollama_starter.start_ollama()

    def download_model(self, _=None):
        """Download a model using the ModelDownloader module"""
        self.model_downloader.download_model()



    def check_model_status(self):
        """Check model status using the ModelStatusChecker module"""
        self.model_status_checker.check_model_status()

    def system_info(self):
        """Show system information using the SystemInfo module"""
        self.system_info.show_system_info()

    def run_gui(self):
        """Launch GUI using the GUILauncher module"""
        self.gui_launcher.launch_gui()

    def open_web_interface(self):
        """Open web interface using the WebInterfaceLauncher module"""
        self.web_interface_launcher.open_web_interface()


def launch_main_application():
    print("\nLaunching DeepSeek R1...")

    import tkinter as tk

    root = tk.Tk()
    root.title("DeepSeek R1")

    try:
        root.iconbitmap("icon.ico")
    except:
        pass

    DeepSeekLauncher(root)

    print("✓ Application started successfully")
    root.mainloop()

    return True

def main():
    print("DeepSeek R1 Launcher")
    print("====================")

    if not check_and_install_dependencies():
        print("\nWarning: Dependency check failed. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")

    if not start_ollama():
        print("\nWarning: Failed to start Ollama. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")

    if not start_api_server():
        print("\nWarning: Failed to start API server. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")

    if not launch_main_application():
        print("\nError: Failed to launch the main application.")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
