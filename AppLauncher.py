
import os
import sys
import subprocess
import time

# Import the new modular components
try:
    from WebInterfaceLauncher import WebInterfaceLauncher
    from Ollama import start_api_server_in_background, OllamaManager
except ImportError as e:
    print(f"Warning: Some modules not found: {e}")
    print("Some functionality may be limited")

    # Fallback classes for missing modules
    class WebInterfaceLauncher:
        def __init__(self, *args, **kwargs): pass
        def open_web_interface(self):
            print("Error: WebInterfaceLauncher module not found")
            return False

    def start_api_server_in_background(port=8765):
        print("Ollama API server module not found, skipping")
        return None

    class OllamaManager:
        @staticmethod
        def check_ollama_running():
            return False

        @staticmethod
        def start_ollama():
            return False

def check_and_install_dependencies():
    print("Checking dependencies...")

    required_packages = [
        "requests",
        "psutil"
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} is installed")
        except ImportError:
            print(f"✗ {package} is not installed")
            missing_packages.append(package)

    if missing_packages:
        print("\nInstalling missing packages...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing_packages)
            print("All dependencies installed successfully!")
        except subprocess.CalledProcessError as e:
            print(f"Error installing dependencies: {e}")
            print("\nPlease try to install them manually using:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    else:
        print("\nAll dependencies are already installed!")

    print("\nChecking if Ollama is installed...")
    try:
        result = subprocess.run(['ollama', 'list'],
                              capture_output=True,
                              text=True,
                              encoding='utf-8')
        if result.returncode == 0:
            print("✓ Ollama is installed")
        else:
            print("✗ Ollama is not installed or not working properly")
            print("Please install Ollama from: https://ollama.com/download/windows")
            return False
    except FileNotFoundError:
        print("✗ Ollama is not installed or not in PATH")
        print("Please install Ollama from: https://ollama.com/download/windows")
        return False

    print("\nDependency check completed!")
    return True

def start_ollama():
    print("\nChecking if Ollama is running...")

    if OllamaManager.check_ollama_running():
        print("✓ Ollama is already running")
        return True

    print("Starting Ollama...")
    if OllamaManager.start_ollama():
        print("✓ Ollama started successfully")
        return True
    else:
        print("✗ Failed to start Ollama")
        return False

def start_api_server():
    print("\nStarting API server...")

    api_thread = start_api_server_in_background()
    if api_thread:
        print("✓ API server started successfully")
        return True
    else:
        print("✗ API server may already be running")
        return True

def launch_web_interface():
    """Launch the web interface directly"""
    print("\nLaunching web interface...")

    # Initialize the web interface launcher
    web_launcher = WebInterfaceLauncher()

    # Check if proxy server is running
    if not web_launcher.check_proxy_running():
        print("Starting proxy server...")
        if not web_launcher.start_proxy_server():
            print("✗ Failed to start proxy server")
            return False

    print("✓ Proxy server is running")

    # Open web interface
    print("Opening web interface in browser...")
    try:
        import webbrowser
        webbrowser.open("http://localhost:8766/")
        print("✓ Web interface opened at http://localhost:8766/")
        print("\nThe web interface is now running!")
        print("You can close this terminal window if you want.")
        print("The web interface will continue running in the background.")

        # Keep the script running to maintain the proxy server
        print("\nPress Ctrl+C to stop the server and exit...")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\nShutting down...")
            return True

    except Exception as e:
        print(f"✗ Failed to open web interface: {e}")
        return False

def main():
    print("DeepSeek R1 Launcher")
    print("====================")

    if not check_and_install_dependencies():
        print("\nWarning: Dependency check failed. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")

    if not start_ollama():
        print("\nWarning: Failed to start Ollama. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")

    if not start_api_server():
        print("\nWarning: Failed to start API server. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")

    if not launch_web_interface():
        print("\nError: Failed to launch the web interface.")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
