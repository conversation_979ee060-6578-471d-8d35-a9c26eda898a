
import os
import sys
import subprocess
import platform
import webbrowser
import tkinter as tk
from tkinter import ttk, messagebox
import requests
import threading
import urllib.parse
import time
import importlib.util

try:
    from Ollama import OllamaManager, start_api_server_in_background, start_proxy_server_in_background
except ImportError:
    print("Ollama manager module not found, functionality will be limited")

    class OllamaManager:
        @staticmethod
        def install_ollama():
            print("Ollama manager module not found, cannot install Ollama")
            return False

        @staticmethod
        def start_ollama():
            print("Ollama manager module not found, cannot start Ollama")
            return False

        @staticmethod
        def check_ollama_running():
            return False

        @staticmethod
        def get_installed_models():
            return []

        @staticmethod
        def download_model(model_name, callback=None):
            print("Ollama manager module not found, cannot download model")
            return False

    def start_api_server_in_background(port=8765):
        print("Ollama API server module not found, skipping")
        return None

    def start_proxy_server_in_background(port=8766):
        print("Ollama proxy server module not found, skipping")
        return None

def check_and_install_dependencies():
    print("Checking dependencies...")

    required_packages = [
        "requests",
        "psutil",
        "tkinter"
    ]

    missing_packages = []
    for package in required_packages:
        try:
            if package == "tkinter":
                import tkinter
                print(f"✓ {package} is installed")
            else:
                __import__(package)
                print(f"✓ {package} is installed")
        except ImportError:
            print(f"✗ {package} is not installed")
            missing_packages.append(package)

    if missing_packages:
        print("\nInstalling missing packages...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing_packages)
            print("All dependencies installed successfully!")
        except subprocess.CalledProcessError as e:
            print(f"Error installing dependencies: {e}")
            print("\nPlease try to install them manually using:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    else:
        print("\nAll dependencies are already installed!")

    print("\nChecking if Ollama is installed...")
    try:
        result = subprocess.run(['ollama', 'list'],
                              capture_output=True,
                              text=True,
                              encoding='utf-8')
        if result.returncode == 0:
            print("✓ Ollama is installed")
        else:
            print("✗ Ollama is not installed or not working properly")
            print("Please install Ollama from: https://ollama.com/download/windows")
            return False
    except FileNotFoundError:
        print("✗ Ollama is not installed or not in PATH")
        print("Please install Ollama from: https://ollama.com/download/windows")
        return False

    print("\nDependency check completed!")
    return True

def start_ollama():
    print("\nChecking if Ollama is running...")

    if OllamaManager.check_ollama_running():
        print("✓ Ollama is already running")
        return True

    print("Starting Ollama...")
    if OllamaManager.start_ollama():
        print("✓ Ollama started successfully")
        return True
    else:
        print("✗ Failed to start Ollama")
        return False

def start_api_server():
    print("\nStarting API server...")

    api_thread = start_api_server_in_background()
    if api_thread:
        print("✓ API server started successfully")
        return True
    else:
        print("✗ API server may already be running")
        return True

class DeepSeekLauncher:

    def __init__(self, root):
        self.root = root
        self.root.title("Chat Launcher")
        self.root.geometry("700x550")
        self.root.minsize(700, 550)

        header_frame = ttk.Frame(root, padding=15)
        header_frame.pack(fill=tk.X)

        title_label = ttk.Label(header_frame, text="Chat Launcher", font=("Arial", 20, "bold"))
        title_label.pack(pady=(0, 5))

        separator = ttk.Separator(root, orient='horizontal')
        separator.pack(fill=tk.X, padx=15, pady=5)

        main_frame = ttk.Frame(root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        buttons_frame.columnconfigure(0, weight=1, uniform='column')
        buttons_frame.columnconfigure(1, weight=1, uniform='column')

        for i in range(4):
            buttons_frame.rowconfigure(i, minsize=50)

        self.create_button(buttons_frame, "Install Ollama", self.install_ollama, 0, 0)
        self.create_button(buttons_frame, "Start Ollama", self.start_ollama, 0, 1)

        self.create_button(buttons_frame, "Download AI Model", self.download_model, 1, 0)
        self.create_button(buttons_frame, "Check Model Status", self.check_model_status, 1, 1)

        self.create_button(buttons_frame, "System Information", self.system_info, 2, 0, columnspan=2)

        self.create_button(buttons_frame, "Run DeepSeek GUI", self.run_gui, 3, 0)
        self.create_button(buttons_frame, "Open Web Interface", self.open_web_interface, 3, 1)

        status_frame = ttk.Frame(root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)

        ttk.Separator(status_frame, orient='horizontal').pack(fill=tk.X, pady=(5, 2))

        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W, padding=(10, 5))
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

        self.check_ollama_status()

    def create_button(self, parent, text, command, row, column, columnspan=1):
        button = ttk.Button(parent, text=text, command=command, width=30)
        button.grid(row=row, column=column, padx=15, pady=10, sticky="nsew", columnspan=columnspan)
        return button

    def check_ollama_status(self):
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                self.status_var.set("Ollama is installed")

                try:
                    response = requests.get("http://localhost:11434/api/tags", timeout=2)
                    if response.status_code == 200:
                        self.status_var.set("Ollama is installed and running")
                except:
                    self.status_var.set("Ollama is installed but not running")
            else:
                self.status_var.set("Ollama is not installed or not in PATH")
        except FileNotFoundError:
            self.status_var.set("Ollama is not installed or not in PATH")

    def install_ollama(self):
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                messagebox.showinfo("Ollama", "Ollama is already installed.")
                return
        except FileNotFoundError:
            pass

        self.status_var.set("Installing Ollama...")

        def install_thread():
            success = OllamaManager.install_ollama()
            if success:
                self.root.after(0, lambda: self.status_var.set("Ollama installed successfully"))
                self.root.after(0, lambda: messagebox.showinfo("Ollama", "Ollama has been installed successfully."))
            else:
                self.root.after(0, lambda: webbrowser.open("https://ollama.com/download/windows"))
                self.root.after(0, lambda: messagebox.showinfo("Install Ollama",
                                                            "Please download and install Ollama from the website that just opened.\n\n"
                                                            "After installation is complete, you may need to restart your computer."))

        threading.Thread(target=install_thread, daemon=True).start()

    def start_ollama(self):
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                messagebox.showinfo("Ollama", "Ollama is already running.")
                return
        except:
            pass

        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode != 0:
                messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
                return
        except FileNotFoundError:
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return

        self.status_var.set("Starting Ollama...")

        def start_ollama_thread():
            try:
                success = OllamaManager.start_ollama()

                if success:
                    self.root.after(0, lambda: self.status_var.set("Ollama started successfully"))
                    self.root.after(0, lambda: messagebox.showinfo("Ollama", "Ollama started successfully."))
                else:
                    try:
                        subprocess.Popen(['ollama', 'serve'],
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      creationflags=subprocess.CREATE_NO_WINDOW)

                        for _ in range(10):
                            try:
                                response = requests.get("http://localhost:11434/api/tags", timeout=1)
                                if response.status_code == 200:
                                    self.root.after(0, lambda: self.status_var.set("Ollama started successfully"))
                                    self.root.after(0, lambda: messagebox.showinfo("Ollama", "Ollama started successfully."))
                                    return
                            except:
                                pass

                            time.sleep(1)

                        self.root.after(0, lambda: self.status_var.set("Failed to start Ollama"))
                        self.root.after(0, lambda: messagebox.showwarning("Ollama", "Ollama may not have started properly. Please check if Ollama is running in the background."))
                    except Exception as e:
                        self.root.after(0, lambda: self.status_var.set("Error starting Ollama"))
                        self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to start Ollama: {str(e)}"))

            except Exception as e:
                self.root.after(0, lambda: self.status_var.set("Error starting Ollama"))
                self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to start Ollama: {str(e)}"))

        threading.Thread(target=start_ollama_thread, daemon=True).start()

    def download_model(self, _=None):
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode != 0:
                messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
                return
        except FileNotFoundError:
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return

        ollama_running = False
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                ollama_running = True
            else:
                start_ollama = messagebox.askyesno("Ollama Not Running",
                                                "Ollama is not running. \n\nDo you want to start Ollama now?")
                if start_ollama:
                    self.start_ollama()
                    self.status_var.set("Waiting for Ollama to start...")
                    for _ in range(10):
                        time.sleep(1)
                        try:
                            response = requests.get("http://localhost:11434/api/tags", timeout=1)
                            if response.status_code == 200:
                                ollama_running = True
                                break
                        except:
                            pass

                    if not ollama_running:
                        messagebox.showwarning("Warning", "Ollama may not have started properly. The download may fail.")
                else:
                    return
        except Exception as e:
            start_ollama = messagebox.askyesno("Ollama Not Running",
                                            f"Ollama is not running or not responding. Error: {str(e)}\n\nDo you want to start Ollama now?")
            if start_ollama:
                self.start_ollama()
                self.status_var.set("Waiting for Ollama to start...")
                for _ in range(10):
                    time.sleep(1)
                    try:
                        response = requests.get("http://localhost:11434/api/tags", timeout=1)
                        if response.status_code == 200:
                            ollama_running = True
                            break
                    except:
                        pass

                if not ollama_running:
                    messagebox.showwarning("Warning", "Ollama may not have started properly. The download may fail.")
            else:
                return

        self.status_var.set("Fetching available models...")

        available_models = [
            "llama2",
            "llama2:7b",
            "llama2:13b",
            "llama2:70b",
            "mistral",
            "mistral:7b",
            "mixtral",
            "mixtral:8x7b",
            "codellama",
            "codellama:7b",
            "codellama:13b",
            "codellama:34b",
            "phi",
            "phi:2",
            "phi:3",
            "gemma:2b",
            "gemma:7b",
            "neural-chat",
            "orca-mini",
            "vicuna",
            "wizard-math",
            "deepseek-coder",
            "deepseek-coder:6.7b",
            "deepseek-coder:33b",
            "deepseek-llm",
            "deepseek-llm:7b",
            "deepseek-llm:67b",
            "qwen",
            "qwen:7b",
            "qwen:14b",
            "qwen:72b",
            "yi",
            "yi:6b",
            "yi:34b"
        ]

        try:
            installed_models = []
            installed_models_sizes = {}

            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=2)
                if response.status_code == 200:
                    models_data = response.json().get("models", [])

                    for model in models_data:
                        model_name = model.get('name')
                        if model_name:
                            installed_models.append(model_name)

                            size_bytes = model.get('size')
                            if size_bytes and str(size_bytes).isdigit():
                                size_gb = float(size_bytes) / (1024 * 1024 * 1024)
                                installed_models_sizes[model_name] = size_gb
            except:
                pass

            if not installed_models:
                result = subprocess.run(['ollama', 'list'],
                                      capture_output=True,
                                      text=True,
                                      encoding='utf-8')

                if result.returncode == 0:
                    for line in result.stdout.strip().split('\n'):
                        if line and not line.startswith('NAME'):
                            model_name = line.split()[0].strip()
                            installed_models.append(model_name)

            if installed_models:
                for model in installed_models:
                    if model in available_models:
                        available_models.remove(model)

                available_models = installed_models + available_models

                self.status_var.set(f"Found {len(installed_models)} installed models")
        except Exception as e:
            pass

        model_dialog = tk.Toplevel(self.root)
        model_dialog.title("Select Model to Download")
        model_dialog.geometry("600x500")
        model_dialog.transient(self.root)
        model_dialog.grab_set()
        model_dialog.resizable(True, True)

        model_dialog.update_idletasks()
        width = model_dialog.winfo_width()
        height = model_dialog.winfo_height()
        x = (model_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (model_dialog.winfo_screenheight() // 2) - (height // 2)
        model_dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        ttk.Label(model_dialog, text="Select a Model to Download", font=("Arial", 12, "bold")).pack(pady=10)
        ttk.Label(model_dialog, text="For your RTX 3060 with 12GB VRAM, any of these models will work well.").pack(pady=5)

        container_frame = ttk.Frame(model_dialog)
        container_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        canvas = tk.Canvas(container_frame, height=250, width=550)
        scrollbar = ttk.Scrollbar(container_frame, orient="vertical", command=canvas.yview)
        models_frame = ttk.Frame(canvas)

        models_frame.bind(
            "<Configure>",
            lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=models_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        selected_model = tk.StringVar(value=available_models[0] if available_models else "llama2")

        for model in available_models:
            if model.startswith("llama2"):
                description = "Meta's open source LLM"
            elif model.startswith("mistral"):
                description = "High-performance open source LLM"
            elif model.startswith("mixtral"):
                description = "Mixture of experts model"
            elif model.startswith("codellama"):
                description = "Code-specialized LLM"
            elif model.startswith("phi"):
                description = "Microsoft's small but powerful LLM"
            elif model.startswith("gemma"):
                description = "Google's lightweight LLM"
            elif model.startswith("neural-chat"):
                description = "Optimized for chat applications"
            elif model.startswith("orca-mini"):
                description = "Small but capable assistant model"
            elif model.startswith("vicuna"):
                description = "Fine-tuned LLaMA model"
            elif model.startswith("wizard"):
                description = "Specialized for math problems"
            elif model.startswith("deepseek"):
                description = "DeepSeek's powerful LLM"
            elif model.startswith("qwen"):
                description = "Alibaba's advanced LLM"
            elif model.startswith("yi"):
                description = "01.AI's powerful LLM"
            else:
                description = "LLM model"

            model_size_gb = None
            if 'installed_models_sizes' in locals() and model in installed_models_sizes:
                model_size_gb = installed_models_sizes[model]
                size_info = f"({model_size_gb:.2f} GB)"
            else:
                if ":7b" in model:
                    size_info = "(~4.1 GB disk / ~7GB VRAM)"
                elif ":13b" in model:
                    size_info = "(~8.2 GB disk / ~13GB VRAM)"
                elif ":34b" in model or ":33b" in model:
                    size_info = "(~19.5 GB disk / ~24GB+ VRAM)"
                elif ":70b" in model or ":67b" in model:
                    size_info = "(~42 GB disk / ~48GB+ VRAM)"
                elif ":2b" in model or ":3b" in model:
                    size_info = "(~1.8 GB disk / ~4GB VRAM)"
                elif ":6b" in model:
                    size_info = "(~3.5 GB disk / ~6GB VRAM)"
                elif ":8x7b" in model:
                    size_info = "(~15 GB disk / ~24GB+ VRAM)"
                elif ":14b" in model:
                    size_info = "(~9 GB disk / ~14GB VRAM)"
                elif ":72b" in model:
                    size_info = "(~45 GB disk / ~48GB+ VRAM)"
                else:
                    size_info = ""

            is_installed = model in installed_models if 'installed_models' in locals() else False

            if is_installed and size_info:
                label_text = f"{model} - {description} {size_info} [INSTALLED]"
            elif is_installed:
                label_text = f"{model} - {description} [INSTALLED]"
            elif size_info:
                label_text = f"{model} - {description} {size_info}"
            else:
                label_text = f"{model} - {description}"

            model_frame = ttk.Frame(models_frame)
            model_frame.pack(fill=tk.X, pady=2)

            if is_installed:
                indicator = ttk.Label(model_frame, text="✓", foreground="green", font=("Arial", 10, "bold"))
                indicator.pack(side=tk.LEFT, padx=(0, 5))
                label_text = f"{model} - {description}"
                if size_info:
                    label_text += f" {size_info}"

            radio_btn = ttk.Radiobutton(
                model_frame,
                text=label_text,
                value=model,
                variable=selected_model
            )
            radio_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Separator(model_dialog, orient='horizontal').pack(fill=tk.X, padx=20, pady=10)

        custom_frame = ttk.Frame(model_dialog)
        custom_frame.pack(fill=tk.X, padx=20, pady=5)

        ttk.Label(custom_frame, text="Or enter a custom model name:").pack(anchor=tk.W)

        custom_model = tk.StringVar()
        custom_entry = ttk.Entry(custom_frame, textvariable=custom_model, width=40)
        custom_entry.pack(fill=tk.X, pady=5)

        buttons_frame = ttk.Frame(model_dialog)
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        dialog_result = {"model": None}

        def on_download():
            if custom_model.get().strip():
                dialog_result["model"] = custom_model.get().strip()
            else:
                dialog_result["model"] = selected_model.get()
            model_dialog.destroy()

        def on_cancel():
            model_dialog.destroy()

        download_button = ttk.Button(buttons_frame, text="Download", command=on_download, width=15)
        cancel_button = ttk.Button(buttons_frame, text="Cancel", command=on_cancel, width=15)

        cancel_button.pack(side=tk.RIGHT, padx=5)
        download_button.pack(side=tk.RIGHT, padx=5)

        self.root.wait_window(model_dialog)

        if dialog_result["model"] is None:
            return

        model_name = dialog_result["model"]

        if ':' in model_name:
            parts = model_name.split(':')
            base_name = parts[0].replace('-', ' ').title()
            version = parts[1].upper() if parts[1].lower() in ['7b', '13b', '34b', '70b'] else parts[1]
            model_display_name = f"{base_name} {version}"
        else:
            model_display_name = model_name.replace('-', ' ').title()

        self.status_var.set(f"Starting download of {model_display_name}...")

        try:
            progress_window = tk.Toplevel(self.root)
            progress_window.title("Downloading Model")
            progress_window.geometry("600x400")
            progress_window.transient(self.root)
            progress_window.grab_set()
            progress_window.resizable(True, True)

            progress_window.update_idletasks()
            width = progress_window.winfo_width()
            height = progress_window.winfo_height()
            x = (progress_window.winfo_screenwidth() // 2) - (width // 2)
            y = (progress_window.winfo_screenheight() // 2) - (height // 2)
            progress_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

            ttk.Label(progress_window, text=f"Downloading {model_display_name}", font=("Arial", 12, "bold")).pack(pady=10)

            progress_bar_frame = ttk.Frame(progress_window, padding=(20, 5))
            progress_bar_frame.pack(fill=tk.X, padx=20)

            progress_percent = tk.StringVar(value="0%")
            progress_label = ttk.Label(progress_bar_frame, textvariable=progress_percent)
            progress_label.pack(side=tk.RIGHT, padx=5)

            progress_bar = ttk.Progressbar(progress_bar_frame, orient="horizontal", length=500, mode="indeterminate")
            progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)
            progress_bar.start(50)

            download_info = tk.StringVar(value="Preparing download...")
            download_info_label = ttk.Label(progress_window, textvariable=download_info)
            download_info_label.pack(pady=5)

            progress_frame = ttk.Frame(progress_window, padding=10)
            progress_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            progress_text = tk.Text(progress_frame, wrap=tk.WORD, height=15, width=70)
            progress_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            scrollbar = ttk.Scrollbar(progress_frame, orient="vertical", command=progress_text.yview)
            scrollbar.pack(side="right", fill="y")
            progress_text.configure(yscrollcommand=scrollbar.set)

            cancel_button = ttk.Button(progress_window, text="Cancel", width=15)
            cancel_button.pack(pady=10)

            cancelled = {"value": False}

            def on_cancel():
                cancelled["value"] = True
                progress_text.insert(tk.END, "\nCancelling download...")
                progress_text.see(tk.END)

            cancel_button.configure(command=on_cancel)

            def update_progress(text):
                progress_text.insert(tk.END, f"{text}\n")
                progress_text.see(tk.END)

            update_progress(f"Starting download of {model_display_name}...")

            animation_active = True

            def animate_progress():
                animation_chars = ["-", "\\", "|", "/"]
                idx = 0
                prep_time = 0
                while animation_active and not cancelled["value"]:
                    char = animation_chars[idx % len(animation_chars)]
                    progress_percent.set(f"{char} Preparing {char}")
                    idx += 1

                    prep_time += 1
                    if prep_time == 20:
                        self.root.after(0, lambda: download_info.set("Preparing download... (Initializing)"))
                    elif prep_time == 40:
                        self.root.after(0, lambda: download_info.set("Preparing download... (This may take a while for large models)"))
                    elif prep_time == 80:
                        self.root.after(0, lambda: download_info.set("Preparing download... (Still working, please be patient)"))
                    elif prep_time % 40 == 0 and prep_time > 80:
                        self.root.after(0, lambda: download_info.set(f"Preparing download... (Working for {prep_time//4} seconds)"))

                    time.sleep(0.25)

            animation_thread = threading.Thread(target=animate_progress, daemon=True)
            animation_thread.start()

            def monitor_download_progress():
                self.root.after(0, lambda: update_progress("Starting download..."))
                self.root.after(0, lambda: download_info.set("Initializing download..."))

                def progress_callback(message):
                    self.root.after(0, lambda: update_progress(message))

                start_time = time.time()
                success = OllamaManager.download_model(model_name, callback=progress_callback)

                if success:
                    self.root.after(0, lambda: progress_bar.stop())
                    self.root.after(0, lambda: progress_bar.configure(mode="determinate", value=100))
                    self.root.after(0, lambda: progress_percent.set("100%"))
                    self.root.after(0, lambda: download_info.set("Download completed successfully!"))
                    self.root.after(0, lambda: update_progress(f"Model {model_name} successfully downloaded!"))
                    self.root.after(0, lambda: self.status_var.set(f"{model_display_name} downloaded successfully"))
                    self.root.after(0, lambda: messagebox.showinfo("Download Complete", f"{model_display_name} downloaded successfully!"))
                    self.root.after(2000, progress_window.destroy)
                else:
                    self.root.after(0, lambda: progress_bar.stop())
                    self.root.after(0, lambda: progress_bar.configure(mode="determinate", value=0))
                    self.root.after(0, lambda: progress_percent.set("Error"))
                    self.root.after(0, lambda: download_info.set("Download failed"))
                    self.root.after(0, lambda: update_progress("Download failed after trying all approaches."))
                    self.root.after(0, lambda: self.status_var.set("Download failed"))
                    self.root.after(0, lambda: messagebox.showerror("Download Failed", f"Failed to download model '{model_name}'."))

            monitor_thread = threading.Thread(target=monitor_download_progress, daemon=True)
            monitor_thread.start()

            animation_active = False

        except Exception as e:
            error_msg = str(e)
            self.status_var.set("Download error")
            messagebox.showerror("Error", f"Download error: {error_msg}")

    def check_model_status(self):
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode != 0:
                messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
                return
        except FileNotFoundError:
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return

        installed_models = []
        try:
            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=2)
                if response.status_code == 200:
                    models_data = response.json().get("models", [])
                    for model in models_data:
                        model_name = model.get('name')
                        if model_name:
                            size_bytes = model.get('size')
                            if size_bytes and str(size_bytes).isdigit():
                                size_gb = float(size_bytes) / (1024 * 1024 * 1024) 
                                installed_models.append({
                                    'name': model_name,
                                    'size': size_gb
                                })
                            else:
                                installed_models.append({
                                    'name': model_name,
                                    'size': None
                                })
            except:
                pass

            if not installed_models:
                result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]: 
                        if line.strip():
                            parts = line.split()
                            if len(parts) >= 2:
                                model_name = parts[0]
                                size_str = parts[1] if len(parts) > 1 else "Unknown"
                                size_gb = None
                                if 'GB' in size_str:
                                    try:
                                        size_gb = float(size_str.replace('GB', '').strip())
                                    except:
                                        pass

                                installed_models.append({
                                    'name': model_name,
                                    'size': size_gb
                                })

        except Exception as e:
            messagebox.showerror("Error", f"Failed to get model information: {str(e)}")
            return
        status_dialog = tk.Toplevel(self.root)
        status_dialog.title("Model Status")
        status_dialog.geometry("500x400")
        status_dialog.transient(self.root)
        status_dialog.grab_set()
        status_dialog.resizable(True, True)
        status_dialog.update_idletasks()
        width = status_dialog.winfo_width()
        height = status_dialog.winfo_height()
        x = (status_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (status_dialog.winfo_screenheight() // 2) - (height // 2)
        status_dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        main_frame = ttk.Frame(status_dialog, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)


        title_label = ttk.Label(main_frame, text="Installed Models", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 15))

        if not installed_models:
            no_models_frame = ttk.Frame(main_frame)
            no_models_frame.pack(fill=tk.BOTH, expand=True)

            ttk.Label(no_models_frame, text="No models are currently installed.",
                     font=("Arial", 12)).pack(pady=20)

            ttk.Label(no_models_frame, text="Click 'Download AI Model' to install a model.",
                     font=("Arial", 10)).pack(pady=5)
        else:
            list_frame = ttk.Frame(main_frame)
            list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            canvas = tk.Canvas(list_frame, height=200)
            scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=canvas.yview)
            models_frame = ttk.Frame(canvas)
            models_frame.bind(
                "<Configure>",
                lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=models_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            scrollbar.pack(side="right", fill="y")
            canvas.pack(side="left", fill="both", expand=True)

            for i, model in enumerate(installed_models):
                model_frame = ttk.Frame(models_frame)
                model_frame.pack(fill=tk.X, pady=5, padx=10)

                icon_label = ttk.Label(model_frame, text="📦", font=("Arial", 12))
                icon_label.pack(side=tk.LEFT, padx=(0, 10))
            
                info_frame = ttk.Frame(model_frame)
                info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

                name_label = ttk.Label(info_frame, text=model['name'], font=("Arial", 11, "bold"))
                name_label.pack(anchor=tk.W)

                if model['size'] is not None:
                    size_text = f"Size: {model['size']:.2f} GB"
                else:
                    size_text = "Size: Unknown"

                size_label = ttk.Label(info_frame, text=size_text, font=("Arial", 9))
                size_label.pack(anchor=tk.W)

                if i < len(installed_models) - 1:
                    ttk.Separator(models_frame, orient='horizontal').pack(fill=tk.X, pady=5, padx=10)

        ok_button = ttk.Button(main_frame, text="OK", command=status_dialog.destroy, width=10)
        ok_button.pack(pady=10)

        self.root.wait_window(status_dialog)

    def system_info(self):
        system = platform.system()
        release = platform.release()
        version = platform.version()
        processor = platform.processor()

        python_version = sys.version.split()[0]

        try:
            import psutil
            memory = psutil.virtual_memory()
            total_memory_gb = memory.total / (1024**3)
            available_memory_gb = memory.available / (1024**3)
            used_memory_gb = memory.used / (1024**3)
            memory_percent = memory.percent
        except ImportError:
            total_memory_gb = None
            available_memory_gb = None
            used_memory_gb = None
            memory_percent = None

        try:
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                gpu_info = "NVIDIA GPU detected"
                for line in lines:
                    if "NVIDIA GeForce RTX" in line or "NVIDIA RTX" in line:
                        gpu_parts = line.strip().split()
                        for i, part in enumerate(gpu_parts):
                            if "RTX" in part and i+1 < len(gpu_parts):
                                gpu_info = f"NVIDIA GeForce {part} {gpu_parts[i+1]}"
                                break
                        break
            else:
                gpu_info = "No NVIDIA GPU detected"
        except FileNotFoundError:
            gpu_info = "NVIDIA GPU drivers not found"
        except Exception:
            gpu_info = "Could not detect GPU"

        sys_info_dialog = tk.Toplevel(self.root)
        sys_info_dialog.title("System Information")
        sys_info_dialog.geometry("650x600") 
        sys_info_dialog.resizable(False, False)
        sys_info_dialog.transient(self.root)  
        sys_info_dialog.grab_set() 

        try:
            sys_info_dialog.iconbitmap("icon.ico")
        except:
            pass

        main_frame = ttk.Frame(sys_info_dialog, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)

        title_label = ttk.Label(main_frame, text="System Information", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 15))

        info_frame = ttk.LabelFrame(main_frame, text="Hardware & Software Details", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        system_frame = ttk.Frame(info_frame)
        system_frame.pack(fill=tk.X, pady=5)
        system_icon = ttk.Label(system_frame, text="🖥️", font=("Arial", 12), width=2, anchor="w")
        system_icon.pack(side=tk.LEFT, padx=(0, 10))
        system_label = ttk.Label(system_frame, text=f"System: {system} {release}", font=("Arial", 10))
        system_label.pack(side=tk.LEFT)

        cpu_frame = ttk.Frame(info_frame)
        cpu_frame.pack(fill=tk.X, pady=5)
        cpu_icon = ttk.Label(cpu_frame, text="⚙️", font=("Arial", 12), width=2, anchor="w")
        cpu_icon.pack(side=tk.LEFT, padx=(0, 10))
        cpu_label = ttk.Label(cpu_frame, text=f"Processor: {processor}", font=("Arial", 10))
        cpu_label.pack(side=tk.LEFT)

        gpu_frame = ttk.Frame(info_frame)
        gpu_frame.pack(fill=tk.X, pady=5)
        gpu_icon = ttk.Label(gpu_frame, text="🎮", font=("Arial", 12), width=2, anchor="w")
        gpu_icon.pack(side=tk.LEFT, padx=(0, 10))

        clean_gpu_info = gpu_info
        if "WDDM" in clean_gpu_info:
            clean_gpu_info = clean_gpu_info.split("WDDM")[0].strip()

        if "NVIDIA GeForce RTX" in clean_gpu_info:
            parts = clean_gpu_info.split()
            for i, part in enumerate(parts):
                if part.isdigit() and i > 0 and "RTX" in parts[i-1]:
                    clean_gpu_info = f"NVIDIA GeForce RTX {part}"
                    break

        if not clean_gpu_info or clean_gpu_info.isspace():
            clean_gpu_info = "NVIDIA GeForce RTX 3060"

        gpu_label = ttk.Label(gpu_frame, text=f"GPU: {clean_gpu_info}", font=("Arial", 10), wraplength=500)
        gpu_label.pack(side=tk.LEFT)

        if total_memory_gb is not None:
            memory_frame = ttk.Frame(info_frame)
            memory_frame.pack(fill=tk.X, pady=5)
            memory_icon = ttk.Label(memory_frame, text="💾", font=("Arial", 12), width=2, anchor="w")
            memory_icon.pack(side=tk.LEFT, padx=(0, 10))
            memory_text = f"Memory: {total_memory_gb:.1f} GB total, {available_memory_gb:.1f} GB available ({memory_percent:.1f}% used)"
            memory_label = ttk.Label(memory_frame, text=memory_text, font=("Arial", 10), wraplength=500)
            memory_label.pack(side=tk.LEFT)

        python_frame = ttk.Frame(info_frame)
        python_frame.pack(fill=tk.X, pady=5)
        python_icon = ttk.Label(python_frame, text="🐍", font=("Arial", 12), width=2, anchor="w")
        python_icon.pack(side=tk.LEFT, padx=(0, 10))
        python_label = ttk.Label(python_frame, text=f"Python: {python_version}", font=("Arial", 10))
        python_label.pack(side=tk.LEFT)

        ttk.Separator(main_frame, orient='horizontal').pack(fill=tk.X, pady=15)

        recommendations_frame = ttk.LabelFrame(main_frame, text="Recommendations", padding=10)
        recommendations_frame.pack(fill=tk.X, padx=5, pady=5)

        if "RTX" in clean_gpu_info:
            rec_text = "✓ Your system has an NVIDIA RTX GPU, which is excellent for running AI models.\n"
            rec_text += "✓ You can run most 7B and 13B models efficiently.\n"
            if "3060" in clean_gpu_info:
                rec_text += "✓ With 12GB VRAM, you can run models up to 13B parameters comfortably."
            elif "4090" in clean_gpu_info or "4080" in clean_gpu_info:
                rec_text += "✓ With your high-end GPU, you can run even 70B models efficiently."
        else:
            rec_text = "⚠ No NVIDIA GPU detected. AI models will run slowly on CPU.\n"
            rec_text += "⚠ Consider using smaller models (2B-7B) for better performance.\n"
            rec_text += "⚠ For best performance, consider getting an NVIDIA RTX GPU."

        if total_memory_gb is not None:
            if total_memory_gb >= 32:
                rec_text += f"\n✓ {total_memory_gb:.0f}GB RAM is excellent for running large AI models."
            elif total_memory_gb >= 16:
                rec_text += f"\n✓ {total_memory_gb:.0f}GB RAM is good for most AI models."
            else:
                rec_text += f"\n⚠ {total_memory_gb:.0f}GB RAM may limit performance with larger models."

        rec_label = ttk.Label(recommendations_frame, text=rec_text, font=("Arial", 9), wraplength=600, justify=tk.LEFT)
        rec_label.pack(anchor=tk.W)

        close_button = ttk.Button(main_frame, text="Close", command=sys_info_dialog.destroy, width=10)
        close_button.pack(pady=15)

    def run_gui(self):
        try:
            from GUI import DeepSeekGUI

            gui_window = tk.Toplevel(self.root)
            gui_app = DeepSeekGUI(gui_window)

        except ImportError:
            messagebox.showerror("Error", "GUI interface module not found. Please ensure GUI.py is available.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch GUI: {str(e)}")

    def open_web_interface(self):
        try:
            start_proxy_server_in_background()
            webbrowser.open("http://localhost:8766/")
            messagebox.showinfo("Web Interface", "Web interface opened in your browser at http://localhost:8766/")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open web interface: {str(e)}")


def launch_main_application():
    print("\nLaunching DeepSeek R1...")

    import tkinter as tk

    root = tk.Tk()
    root.title("DeepSeek R1")

    try:
        root.iconbitmap("icon.ico")
    except:
        pass

    app = DeepSeekLauncher(root)

    print("✓ Application started successfully")
    root.mainloop()

    return True

def main():
    print("DeepSeek R1 Launcher")
    print("====================")

    if not check_and_install_dependencies():
        print("\nWarning: Dependency check failed. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")

    if not start_ollama():
        print("\nWarning: Failed to start Ollama. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")

    if not start_api_server():
        print("\nWarning: Failed to start API server. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")

    if not launch_main_application():
        print("\nError: Failed to launch the main application.")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
