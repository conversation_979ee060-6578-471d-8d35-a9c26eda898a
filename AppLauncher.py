
import os
import sys
import subprocess
import platform 
import webbrowser
import tkinter as tk
from tkinter import ttk, messagebox
import requests
import threading
import urllib.parse
import time
import importlib.util

try:
    from Ollama import OllamaManager, start_api_server_in_background, start_proxy_server_in_background
except ImportError:
    print("Ollama manager module not found, functionality will be limited")

    class OllamaManager:
        @staticmethod
        def install_ollama():
            print("Ollama manager module not found, cannot install Ollama")
            return False

        @staticmethod
        def start_ollama():
            print("Ollama manager module not found, cannot start Ollama")
            return False

        @staticmethod
        def check_ollama_running():
            return False

        @staticmethod
        def get_installed_models():
            return []

        @staticmethod
        def download_model(model_name, callback=None):
            print("Ollama manager module not found, cannot download model")
            return False

    def start_api_server_in_background(port=8765):
        print("Ollama API server module not found, skipping")
        return None

    def start_proxy_server_in_background(port=8766):
        print("Ollama proxy server module not found, skipping")
        return None

def check_and_install_dependencies():
    """Check if required dependencies are installed and install them if needed."""
    print("Checking dependencies...")

    required_packages = [
        "requests",
        "psutil",
        "tkinter"
    ]

    missing_packages = []
    for package in required_packages:
        try:
            if package == "tkinter":
                import tkinter
                print(f"✓ {package} is installed")
            else:
                __import__(package)
                print(f"✓ {package} is installed")
        except ImportError:
            print(f"✗ {package} is not installed")
            missing_packages.append(package)

    if missing_packages:
        print("\nInstalling missing packages...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing_packages)
            print("All dependencies installed successfully!")
        except subprocess.CalledProcessError as e:
            print(f"Error installing dependencies: {e}")
            print("\nPlease try to install them manually using:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    else:
        print("\nAll dependencies are already installed!")

    print("\nChecking if Ollama is installed...")
    try:
        result = subprocess.run(['ollama', 'list'],
                              capture_output=True,
                              text=True,
                              encoding='utf-8')
        if result.returncode == 0:
            print("✓ Ollama is installed")
        else:
            print("✗ Ollama is not installed or not working properly")
            print("Please install Ollama from: https://ollama.com/download/windows")
            return False
    except FileNotFoundError:
        print("✗ Ollama is not installed or not in PATH")
        print("Please install Ollama from: https://ollama.com/download/windows")
        return False

    print("\nDependency check completed!")
    return True

def start_ollama():
    """Start Ollama if it's not already running."""
    print("\nChecking if Ollama is running...")

    if OllamaManager.check_ollama_running():
        print("✓ Ollama is already running")
        return True

    print("Starting Ollama...")
    if OllamaManager.start_ollama():
        print("✓ Ollama started successfully")
        return True
    else:
        print("✗ Failed to start Ollama")
        return False

def start_api_server():
    """Start the API server in the background."""
    print("\nStarting API server...")

    # Start the API server
    api_thread = start_api_server_in_background()
    if api_thread:
        print("✓ API server started successfully")
        return True
    else:
        print("✗ API server may already be running")
        return True  # Return True anyway since it might be running

class DeepSeekLauncher:
    """Main launcher GUI for the DeepSeek R1 application"""

    def __init__(self, root):
        self.root = root
        self.root.title("Chat Launcher")
        self.root.geometry("700x550")
        self.root.minsize(700, 550)

        # Set title and header with better styling
        header_frame = ttk.Frame(root, padding=15)
        header_frame.pack(fill=tk.X)

        # Title with larger, bolder font
        title_label = ttk.Label(header_frame, text="Chat Launcher", font=("Arial", 20, "bold"))
        title_label.pack(pady=(0, 5))

        # Add a more prominent separator
        separator = ttk.Separator(root, orient='horizontal')
        separator.pack(fill=tk.X, padx=15, pady=5)

        # Create main frame
        main_frame = ttk.Frame(root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Configure grid to make columns equal width
        buttons_frame.columnconfigure(0, weight=1, uniform='column')
        buttons_frame.columnconfigure(1, weight=1, uniform='column')

        # Add more space between rows
        for i in range(4):
            buttons_frame.rowconfigure(i, minsize=50)

        # Create buttons - grouped logically
        # Row 0: Ollama setup buttons
        self.create_button(buttons_frame, "Install Ollama", self.install_ollama, 0, 0)
        self.create_button(buttons_frame, "Start Ollama", self.start_ollama, 0, 1)

        # Row 1: Model management buttons
        self.create_button(buttons_frame, "Download AI Model", self.download_model, 1, 0)
        self.create_button(buttons_frame, "Check Model Status", self.check_model_status, 1, 1)

        # Row 2: System information button (centered)
        self.create_button(buttons_frame, "System Information", self.system_info, 2, 0, columnspan=2)

        # Row 3: Interface buttons (at the bottom)
        self.create_button(buttons_frame, "Run DeepSeek GUI", self.run_gui, 3, 0)
        self.create_button(buttons_frame, "Open Web Interface", self.open_web_interface, 3, 1)

        # Status bar with better styling
        status_frame = ttk.Frame(root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)

        # Add a separator above the status bar
        ttk.Separator(status_frame, orient='horizontal').pack(fill=tk.X, pady=(5, 2))

        # Status bar with better styling
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W, padding=(10, 5))
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

        # Check if Ollama is installed and running
        self.check_ollama_status()

    def create_button(self, parent, text, command, row, column, columnspan=1):
        """Create a button with consistent styling"""
        button = ttk.Button(parent, text=text, command=command, width=30)
        button.grid(row=row, column=column, padx=15, pady=10, sticky="nsew", columnspan=columnspan)
        return button

    def check_ollama_status(self):
        """Check if Ollama is installed and running"""
        # Check if Ollama is installed
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                self.status_var.set("Ollama is installed")

                # Check if Ollama is running
                try:
                    response = requests.get("http://localhost:11434/api/tags", timeout=2)
                    if response.status_code == 200:
                        self.status_var.set("Ollama is installed and running")
                except:
                    self.status_var.set("Ollama is installed but not running")
            else:
                self.status_var.set("Ollama is not installed or not in PATH")
        except FileNotFoundError:
            self.status_var.set("Ollama is not installed or not in PATH")

    def install_ollama(self):
        """Install Ollama"""
        # Check if Ollama is already installed
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                messagebox.showinfo("Ollama", "Ollama is already installed.")
                return
        except FileNotFoundError:
            pass

        # Use the OllamaManager to install Ollama
        self.status_var.set("Installing Ollama...")

        def install_thread():
            success = OllamaManager.install_ollama()
            if success:
                self.root.after(0, lambda: self.status_var.set("Ollama installed successfully"))
                self.root.after(0, lambda: messagebox.showinfo("Ollama", "Ollama has been installed successfully."))
            else:
                # Fallback to opening the download page
                self.root.after(0, lambda: webbrowser.open("https://ollama.com/download/windows"))
                self.root.after(0, lambda: messagebox.showinfo("Install Ollama",
                                                            "Please download and install Ollama from the website that just opened.\n\n"
                                                            "After installation is complete, you may need to restart your computer."))

        # Start in a separate thread
        threading.Thread(target=install_thread, daemon=True).start()

    def start_ollama(self):
        """Start Ollama"""
        # Check if Ollama is already running
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                messagebox.showinfo("Ollama", "Ollama is already running.")
                return
        except:
            pass

        # Check if Ollama is installed
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode != 0:
                messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
                return
        except FileNotFoundError:
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return

        # Start Ollama
        self.status_var.set("Starting Ollama...")

        def start_ollama_thread():
            try:
                # Use the OllamaManager to start Ollama
                success = OllamaManager.start_ollama()

                if success:
                    self.root.after(0, lambda: self.status_var.set("Ollama started successfully"))
                    self.root.after(0, lambda: messagebox.showinfo("Ollama", "Ollama started successfully."))
                else:
                    # Fallback to the old method if OllamaManager fails
                    try:
                        # Start Ollama in a separate process
                        subprocess.Popen(['ollama', 'serve'],
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      creationflags=subprocess.CREATE_NO_WINDOW)

                        # Wait for Ollama to start
                        for _ in range(10):  # Try for 10 seconds
                            try:
                                response = requests.get("http://localhost:11434/api/tags", timeout=1)
                                if response.status_code == 200:
                                    self.root.after(0, lambda: self.status_var.set("Ollama started successfully"))
                                    self.root.after(0, lambda: messagebox.showinfo("Ollama", "Ollama started successfully."))
                                    return
                            except:
                                pass

                            # Wait a bit before trying again
                            time.sleep(1)

                        self.root.after(0, lambda: self.status_var.set("Failed to start Ollama"))
                        self.root.after(0, lambda: messagebox.showwarning("Ollama", "Ollama may not have started properly. Please check if Ollama is running in the background."))
                    except Exception as e:
                        self.root.after(0, lambda: self.status_var.set("Error starting Ollama"))
                        self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to start Ollama: {str(e)}"))

            except Exception as e:
                self.root.after(0, lambda: self.status_var.set("Error starting Ollama"))
                self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to start Ollama: {str(e)}"))

        # Start in a separate thread
        threading.Thread(target=start_ollama_thread, daemon=True).start()

    def download_model(self, _=None):
        """Download AI model using the optimized direct download method"""
        # Check if Ollama is installed
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode != 0:
                messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
                return
        except FileNotFoundError:
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return

        # Check if Ollama is running
        ollama_running = False
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                ollama_running = True
            else:
                start_ollama = messagebox.askyesno("Ollama Not Running",
                                                "Ollama is not running. \n\nDo you want to start Ollama now?")
                if start_ollama:
                    self.start_ollama()
                    # Wait for Ollama to start
                    self.status_var.set("Waiting for Ollama to start...")
                    for _ in range(10):  # Try for 10 seconds
                        time.sleep(1)
                        try:
                            response = requests.get("http://localhost:11434/api/tags", timeout=1)
                            if response.status_code == 200:
                                ollama_running = True
                                break
                        except:
                            pass

                    if not ollama_running:
                        messagebox.showwarning("Warning", "Ollama may not have started properly. The download may fail.")
                else:
                    return
        except Exception as e:
            start_ollama = messagebox.askyesno("Ollama Not Running",
                                            f"Ollama is not running or not responding. Error: {str(e)}\n\nDo you want to start Ollama now?")
            if start_ollama:
                self.start_ollama()
                # Wait for Ollama to start
                self.status_var.set("Waiting for Ollama to start...")
                for _ in range(10):  # Try for 10 seconds
                    time.sleep(1)
                    try:
                        response = requests.get("http://localhost:11434/api/tags", timeout=1)
                        if response.status_code == 200:
                            ollama_running = True
                            break
                    except:
                        pass

                if not ollama_running:
                    messagebox.showwarning("Warning", "Ollama may not have started properly. The download may fail.")
            else:
                return

        # Get the list of available models
        self.status_var.set("Fetching available models...")

        # Use a default list of popular models
        available_models = [
            "llama2",
            "llama2:7b",
            "llama2:13b",
            "llama2:70b",
            "mistral",
            "mistral:7b",
            "mixtral",
            "mixtral:8x7b",
            "codellama",
            "codellama:7b",
            "codellama:13b",
            "codellama:34b",
            "phi",
            "phi:2",
            "phi:3",
            "gemma:2b",
            "gemma:7b",
            "neural-chat",
            "orca-mini",
            "vicuna",
            "wizard-math",
            "deepseek-coder",
            "deepseek-coder:6.7b",
            "deepseek-coder:33b",
            "deepseek-llm",
            "deepseek-llm:7b",
            "deepseek-llm:67b",
            "qwen",
            "qwen:7b",
            "qwen:14b",
            "qwen:72b",
            "yi",
            "yi:6b",
            "yi:34b"
        ]

        # Try to get the list of already downloaded models with their sizes
        try:
            # First try to get model sizes from the API
            installed_models = []
            installed_models_sizes = {}

            try:
                # Get model info from Ollama API
                response = requests.get("http://localhost:11434/api/tags", timeout=2)
                if response.status_code == 200:
                    models_data = response.json().get("models", [])

                    for model in models_data:
                        model_name = model.get('name')
                        if model_name:
                            installed_models.append(model_name)

                            # Get size in GB if available
                            size_bytes = model.get('size')
                            if size_bytes and str(size_bytes).isdigit():
                                size_gb = float(size_bytes) / (1024 * 1024 * 1024)  # Convert bytes to GB
                                installed_models_sizes[model_name] = size_gb
            except:
                # If API fails, fall back to command line
                pass

            # If API failed or returned no models, use the command line approach
            if not installed_models:
                # Use the direct command line approach to get installed models
                result = subprocess.run(['ollama', 'list'],
                                      capture_output=True,
                                      text=True,
                                      encoding='utf-8')

                if result.returncode == 0:
                    # Parse the output to get model names
                    for line in result.stdout.strip().split('\n'):
                        if line and not line.startswith('NAME'):
                            # Extract the model name from the first column
                            model_name = line.split()[0].strip()
                            installed_models.append(model_name)

            # Add installed models to the beginning of the list
            if installed_models:
                # Remove duplicates
                for model in installed_models:
                    if model in available_models:
                        available_models.remove(model)

                # Add installed models at the beginning
                available_models = installed_models + available_models

                # Update status bar instead of showing popup
                self.status_var.set(f"Found {len(installed_models)} installed models")
        except Exception as e:
            # Just continue with the default list
            pass

        # Create a custom dialog to select which model to download
        model_dialog = tk.Toplevel(self.root)
        model_dialog.title("Select Model to Download")
        model_dialog.geometry("600x500")
        model_dialog.transient(self.root)  # Set to be on top of the main window
        model_dialog.grab_set()  # Modal dialog
        model_dialog.resizable(True, True)  # Allow resizing

        # Center the dialog
        model_dialog.update_idletasks()
        width = model_dialog.winfo_width()
        height = model_dialog.winfo_height()
        x = (model_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (model_dialog.winfo_screenheight() // 2) - (height // 2)
        model_dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # Add a label
        ttk.Label(model_dialog, text="Select a Model to Download", font=("Arial", 12, "bold")).pack(pady=10)
        ttk.Label(model_dialog, text="For your RTX 3060 with 12GB VRAM, any of these models will work well.").pack(pady=5)

        # Create a frame for the model options with scrollbar
        container_frame = ttk.Frame(model_dialog)
        container_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Add a canvas for scrolling
        canvas = tk.Canvas(container_frame, height=250, width=550)
        scrollbar = ttk.Scrollbar(container_frame, orient="vertical", command=canvas.yview)
        models_frame = ttk.Frame(canvas)

        # Configure scrolling
        models_frame.bind(
            "<Configure>",
            lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=models_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack the scrollbar and canvas
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # Variable to store the selected model
        selected_model = tk.StringVar(value=available_models[0] if available_models else "llama2")

        # Add radio buttons for each model option with descriptions
        for model in available_models:
            # Create a more descriptive label
            if model.startswith("llama2"):
                description = "Meta's open source LLM"
            elif model.startswith("mistral"):
                description = "High-performance open source LLM"
            elif model.startswith("mixtral"):
                description = "Mixture of experts model"
            elif model.startswith("codellama"):
                description = "Code-specialized LLM"
            elif model.startswith("phi"):
                description = "Microsoft's small but powerful LLM"
            elif model.startswith("gemma"):
                description = "Google's lightweight LLM"
            elif model.startswith("neural-chat"):
                description = "Optimized for chat applications"
            elif model.startswith("orca-mini"):
                description = "Small but capable assistant model"
            elif model.startswith("vicuna"):
                description = "Fine-tuned LLaMA model"
            elif model.startswith("wizard"):
                description = "Specialized for math problems"
            elif model.startswith("deepseek"):
                description = "DeepSeek's powerful LLM"
            elif model.startswith("qwen"):
                description = "Alibaba's advanced LLM"
            elif model.startswith("yi"):
                description = "01.AI's powerful LLM"
            else:
                description = "LLM model"

            # Add size information if available
            # First check if this is an installed model with actual size data
            model_size_gb = None
            if 'installed_models_sizes' in locals() and model in installed_models_sizes:
                model_size_gb = installed_models_sizes[model]
                size_info = f"({model_size_gb:.2f} GB)"
            else:
                # Use estimated sizes based on model name
                if ":7b" in model:
                    size_info = "(~4.1 GB disk / ~7GB VRAM)"
                elif ":13b" in model:
                    size_info = "(~8.2 GB disk / ~13GB VRAM)"
                elif ":34b" in model or ":33b" in model:
                    size_info = "(~19.5 GB disk / ~24GB+ VRAM)"
                elif ":70b" in model or ":67b" in model:
                    size_info = "(~42 GB disk / ~48GB+ VRAM)"
                elif ":2b" in model or ":3b" in model:
                    size_info = "(~1.8 GB disk / ~4GB VRAM)"
                elif ":6b" in model:
                    size_info = "(~3.5 GB disk / ~6GB VRAM)"
                elif ":8x7b" in model:
                    size_info = "(~15 GB disk / ~24GB+ VRAM)"
                elif ":14b" in model:
                    size_info = "(~9 GB disk / ~14GB VRAM)"
                elif ":72b" in model:
                    size_info = "(~45 GB disk / ~48GB+ VRAM)"
                else:
                    size_info = ""

            # Create the full label text
            # Check if this is an installed model
            is_installed = model in installed_models if 'installed_models' in locals() else False

            # Add installed indicator and size info
            if is_installed and size_info:
                label_text = f"{model} - {description} {size_info} [INSTALLED]"
            elif is_installed:
                label_text = f"{model} - {description} [INSTALLED]"
            elif size_info:
                label_text = f"{model} - {description} {size_info}"
            else:
                label_text = f"{model} - {description}"

            # Create a frame for each model to allow for better styling
            model_frame = ttk.Frame(models_frame)
            model_frame.pack(fill=tk.X, pady=2)

            # Add a visual indicator for installed models
            if is_installed:
                # Add a checkmark or indicator icon for installed models
                indicator = ttk.Label(model_frame, text="✓", foreground="green", font=("Arial", 10, "bold"))
                indicator.pack(side=tk.LEFT, padx=(0, 5))
                # Use bold text for installed models
                label_text = f"{model} - {description}"
                if size_info:
                    label_text += f" {size_info}"

            # Create the radio button
            radio_btn = ttk.Radiobutton(
                model_frame,
                text=label_text,
                value=model,
                variable=selected_model
            )
            radio_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Add a separator
        ttk.Separator(model_dialog, orient='horizontal').pack(fill=tk.X, padx=20, pady=10)

        # Add a custom model entry field
        custom_frame = ttk.Frame(model_dialog)
        custom_frame.pack(fill=tk.X, padx=20, pady=5)

        ttk.Label(custom_frame, text="Or enter a custom model name:").pack(anchor=tk.W)

        custom_model = tk.StringVar()
        custom_entry = ttk.Entry(custom_frame, textvariable=custom_model, width=40)
        custom_entry.pack(fill=tk.X, pady=5)

        # Add buttons
        buttons_frame = ttk.Frame(model_dialog)
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        # Variable to store the dialog result
        dialog_result = {"model": None}

        # Function to handle download button click
        def on_download():
            # Check if custom model is entered
            if custom_model.get().strip():
                dialog_result["model"] = custom_model.get().strip()
            else:
                dialog_result["model"] = selected_model.get()
            model_dialog.destroy()

        # Function to handle cancel button click
        def on_cancel():
            model_dialog.destroy()

        # Add buttons - note the order is important for proper display
        download_button = ttk.Button(buttons_frame, text="Download", command=on_download, width=15)
        cancel_button = ttk.Button(buttons_frame, text="Cancel", command=on_cancel, width=15)

        # Pack buttons in reverse order for right alignment
        cancel_button.pack(side=tk.RIGHT, padx=5)
        download_button.pack(side=tk.RIGHT, padx=5)

        # Wait for the dialog to be closed
        self.root.wait_window(model_dialog)

        # Check if a model was selected
        if dialog_result["model"] is None:
            return  # User cancelled

        # Set the model name and display name
        model_name = dialog_result["model"]

        # Create a more user-friendly display name
        if ':' in model_name:
            # Split by colon to get the base name and version
            parts = model_name.split(':')
            base_name = parts[0].replace('-', ' ').title()
            version = parts[1].upper() if parts[1].lower() in ['7b', '13b', '34b', '70b'] else parts[1]
            model_display_name = f"{base_name} {version}"
        else:
            # Just capitalize and replace hyphens with spaces
            model_display_name = model_name.replace('-', ' ').title()

        # Download the model with progress window
        self.status_var.set(f"Starting download of {model_display_name}...")

        try:
            # Create a progress window
            progress_window = tk.Toplevel(self.root)
            progress_window.title("Downloading Model")
            progress_window.geometry("600x400")
            progress_window.transient(self.root)  # Set to be on top of the main window
            progress_window.grab_set()  # Modal dialog
            progress_window.resizable(True, True)  # Allow resizing

            # Center the window
            progress_window.update_idletasks()
            width = progress_window.winfo_width()
            height = progress_window.winfo_height()
            x = (progress_window.winfo_screenwidth() // 2) - (width // 2)
            y = (progress_window.winfo_screenheight() // 2) - (height // 2)
            progress_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

            # Add a label
            ttk.Label(progress_window, text=f"Downloading {model_display_name}", font=("Arial", 12, "bold")).pack(pady=10)

            # Add progress bar
            progress_bar_frame = ttk.Frame(progress_window, padding=(20, 5))
            progress_bar_frame.pack(fill=tk.X, padx=20)

            # Progress percentage label
            progress_percent = tk.StringVar(value="0%")
            progress_label = ttk.Label(progress_bar_frame, textvariable=progress_percent)
            progress_label.pack(side=tk.RIGHT, padx=5)

            # Progress bar
            progress_bar = ttk.Progressbar(progress_bar_frame, orient="horizontal", length=500, mode="indeterminate")
            progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)
            progress_bar.start(50)  # Start the animation

            # Download size info
            download_info = tk.StringVar(value="Preparing download...")
            download_info_label = ttk.Label(progress_window, textvariable=download_info)
            download_info_label.pack(pady=5)

            # Add a progress text area
            progress_frame = ttk.Frame(progress_window, padding=10)
            progress_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            progress_text = tk.Text(progress_frame, wrap=tk.WORD, height=15, width=70)
            progress_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            # Add a scrollbar
            scrollbar = ttk.Scrollbar(progress_frame, orient="vertical", command=progress_text.yview)
            scrollbar.pack(side="right", fill="y")
            progress_text.configure(yscrollcommand=scrollbar.set)

            # Add a cancel button
            cancel_button = ttk.Button(progress_window, text="Cancel", width=15)
            cancel_button.pack(pady=10)

            # Variable to track if download was cancelled
            cancelled = {"value": False}

            # Function to handle cancel button click
            def on_cancel():
                cancelled["value"] = True
                progress_text.insert(tk.END, "\nCancelling download...")
                progress_text.see(tk.END)

            cancel_button.configure(command=on_cancel)

            # Update the progress text
            def update_progress(text):
                progress_text.insert(tk.END, f"{text}\n")
                progress_text.see(tk.END)

            # Initial progress message
            update_progress(f"Starting download of {model_display_name}...")

            # Create a simple animation for the "Preparing" phase
            animation_active = True

            def animate_progress():
                animation_chars = ["-", "\\", "|", "/"]
                idx = 0
                prep_time = 0
                while animation_active and not cancelled["value"]:
                    char = animation_chars[idx % len(animation_chars)]
                    progress_percent.set(f"{char} Preparing {char}")
                    idx += 1

                    # Update the info message based on how long we've been preparing
                    prep_time += 1
                    if prep_time == 20:  # After about 5 seconds
                        self.root.after(0, lambda: download_info.set("Preparing download... (Initializing)"))
                    elif prep_time == 40:  # After about 10 seconds
                        self.root.after(0, lambda: download_info.set("Preparing download... (This may take a while for large models)"))
                    elif prep_time == 80:  # After about 20 seconds
                        self.root.after(0, lambda: download_info.set("Preparing download... (Still working, please be patient)"))
                    elif prep_time % 40 == 0 and prep_time > 80:  # Every 10 seconds after that
                        self.root.after(0, lambda: download_info.set(f"Preparing download... (Working for {prep_time//4} seconds)"))

                    time.sleep(0.25)

            # Start animation in a separate thread
            animation_thread = threading.Thread(target=animate_progress, daemon=True)
            animation_thread.start()

            # Create a progress monitoring thread
            def monitor_download_progress():
                # Update UI
                self.root.after(0, lambda: update_progress("Starting download..."))
                self.root.after(0, lambda: download_info.set("Initializing download..."))

                # Use the OllamaManager to download with callback
                def progress_callback(message):
                    self.root.after(0, lambda: update_progress(message))

                # Start the download
                start_time = time.time()
                success = OllamaManager.download_model(model_name, callback=progress_callback)

                if success:
                    # Download successful
                    self.root.after(0, lambda: progress_bar.stop())
                    self.root.after(0, lambda: progress_bar.configure(mode="determinate", value=100))
                    self.root.after(0, lambda: progress_percent.set("100%"))
                    self.root.after(0, lambda: download_info.set("Download completed successfully!"))
                    self.root.after(0, lambda: update_progress(f"Model {model_name} successfully downloaded!"))
                    self.root.after(0, lambda: self.status_var.set(f"{model_display_name} downloaded successfully"))
                    self.root.after(0, lambda: messagebox.showinfo("Download Complete", f"{model_display_name} downloaded successfully!"))
                    # Close the progress window after a delay
                    self.root.after(2000, progress_window.destroy)
                else:
                    # Download failed
                    self.root.after(0, lambda: progress_bar.stop())
                    self.root.after(0, lambda: progress_bar.configure(mode="determinate", value=0))
                    self.root.after(0, lambda: progress_percent.set("Error"))
                    self.root.after(0, lambda: download_info.set("Download failed"))
                    self.root.after(0, lambda: update_progress("Download failed after trying all approaches."))
                    self.root.after(0, lambda: self.status_var.set("Download failed"))
                    self.root.after(0, lambda: messagebox.showerror("Download Failed", f"Failed to download model '{model_name}'."))

            # Start the progress monitoring in a separate thread
            monitor_thread = threading.Thread(target=monitor_download_progress, daemon=True)
            monitor_thread.start()

            # Stop animation since we're using our own progress monitoring
            animation_active = False

        except Exception as e:
            error_msg = str(e)
            self.status_var.set("Download error")
            messagebox.showerror("Error", f"Download error: {error_msg}")

    def check_model_status(self):
        """Check the status of installed models with detailed information"""
        # Check if Ollama is installed
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode != 0:
                messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
                return
        except FileNotFoundError:
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return

        # Get installed models
        installed_models = []
        try:
            # Try to get model info from the API first
            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=2)
                if response.status_code == 200:
                    models_data = response.json().get("models", [])
                    for model in models_data:
                        model_name = model.get('name')
                        if model_name:
                            # Get size in GB if available
                            size_bytes = model.get('size')
                            if size_bytes and str(size_bytes).isdigit():
                                size_gb = float(size_bytes) / (1024 * 1024 * 1024)  # Convert bytes to GB
                                installed_models.append({
                                    'name': model_name,
                                    'size': size_gb
                                })
                            else:
                                installed_models.append({
                                    'name': model_name,
                                    'size': None
                                })
            except:
                # If API fails, fall back to command line
                pass

            # If API failed or returned no models, use the command line approach
            if not installed_models:
                result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
                if result.returncode == 0:
                    # Parse the output to get model names and sizes
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # Skip header
                        if line.strip():
                            parts = line.split()
                            if len(parts) >= 2:
                                model_name = parts[0]
                                size_str = parts[1] if len(parts) > 1 else "Unknown"

                                # Try to parse size
                                size_gb = None
                                if 'GB' in size_str:
                                    try:
                                        size_gb = float(size_str.replace('GB', '').strip())
                                    except:
                                        pass

                                installed_models.append({
                                    'name': model_name,
                                    'size': size_gb
                                })

        except Exception as e:
            messagebox.showerror("Error", f"Failed to get model information: {str(e)}")
            return

        # Create a detailed model status dialog
        status_dialog = tk.Toplevel(self.root)
        status_dialog.title("Model Status")
        status_dialog.geometry("500x400")
        status_dialog.transient(self.root)
        status_dialog.grab_set()
        status_dialog.resizable(True, True)

        # Center the dialog
        status_dialog.update_idletasks()
        width = status_dialog.winfo_width()
        height = status_dialog.winfo_height()
        x = (status_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (status_dialog.winfo_screenheight() // 2) - (height // 2)
        status_dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # Create main frame
        main_frame = ttk.Frame(status_dialog, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="Installed Models", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 15))

        if not installed_models:
            # No models installed
            no_models_frame = ttk.Frame(main_frame)
            no_models_frame.pack(fill=tk.BOTH, expand=True)

            ttk.Label(no_models_frame, text="No models are currently installed.",
                     font=("Arial", 12)).pack(pady=20)

            ttk.Label(no_models_frame, text="Click 'Download AI Model' to install a model.",
                     font=("Arial", 10)).pack(pady=5)
        else:
            # Create a frame for the model list with scrollbar
            list_frame = ttk.Frame(main_frame)
            list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            # Create a canvas and scrollbar for the model list
            canvas = tk.Canvas(list_frame, height=200)
            scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=canvas.yview)
            models_frame = ttk.Frame(canvas)

            # Configure scrolling
            models_frame.bind(
                "<Configure>",
                lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=models_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # Pack the scrollbar and canvas
            scrollbar.pack(side="right", fill="y")
            canvas.pack(side="left", fill="both", expand=True)

            # Add each model to the list
            for i, model in enumerate(installed_models):
                model_frame = ttk.Frame(models_frame)
                model_frame.pack(fill=tk.X, pady=5, padx=10)

                # Model icon
                icon_label = ttk.Label(model_frame, text="📦", font=("Arial", 12))
                icon_label.pack(side=tk.LEFT, padx=(0, 10))

                # Model info frame
                info_frame = ttk.Frame(model_frame)
                info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

                # Model name
                name_label = ttk.Label(info_frame, text=model['name'], font=("Arial", 11, "bold"))
                name_label.pack(anchor=tk.W)

                # Model size
                if model['size'] is not None:
                    size_text = f"Size: {model['size']:.2f} GB"
                else:
                    size_text = "Size: Unknown"

                size_label = ttk.Label(info_frame, text=size_text, font=("Arial", 9))
                size_label.pack(anchor=tk.W)

                # Add a separator between models (except for the last one)
                if i < len(installed_models) - 1:
                    ttk.Separator(models_frame, orient='horizontal').pack(fill=tk.X, pady=5, padx=10)

        # OK button
        ok_button = ttk.Button(main_frame, text="OK", command=status_dialog.destroy, width=10)
        ok_button.pack(pady=10)

        # Wait for the dialog to be closed
        self.root.wait_window(status_dialog)

    def system_info(self):
        """Show detailed system information with memory and requirements"""
        # Get system info
        system = platform.system()
        release = platform.release()
        version = platform.version()
        processor = platform.processor()

        # Get Python info
        python_version = sys.version.split()[0]

        # Get memory information
        try:
            import psutil
            memory = psutil.virtual_memory()
            total_memory_gb = memory.total / (1024**3)
            available_memory_gb = memory.available / (1024**3)
            used_memory_gb = memory.used / (1024**3)
            memory_percent = memory.percent
        except ImportError:
            # Fallback if psutil is not available
            total_memory_gb = None
            available_memory_gb = None
            used_memory_gb = None
            memory_percent = None

        # Check for NVIDIA GPU
        try:
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                # Extract GPU info
                lines = result.stdout.strip().split('\n')
                gpu_info = "NVIDIA GPU detected"
                for line in lines:
                    if "NVIDIA GeForce RTX" in line or "NVIDIA RTX" in line:
                        # Extract just the GPU model name without WDDM and other technical details
                        gpu_parts = line.strip().split()
                        for i, part in enumerate(gpu_parts):
                            if "RTX" in part and i+1 < len(gpu_parts):
                                gpu_info = f"NVIDIA GeForce {part} {gpu_parts[i+1]}"
                                break
                        break
            else:
                gpu_info = "No NVIDIA GPU detected"
        except FileNotFoundError:
            gpu_info = "NVIDIA GPU drivers not found"
        except Exception:
            gpu_info = "Could not detect GPU"

        # Create a custom dialog with better formatting
        sys_info_dialog = tk.Toplevel(self.root)
        sys_info_dialog.title("System Information")
        sys_info_dialog.geometry("650x600")  # Larger size for more content
        sys_info_dialog.resizable(False, False)
        sys_info_dialog.transient(self.root)  # Set as transient to main window
        sys_info_dialog.grab_set()  # Make it modal

        # Set icon if available
        try:
            sys_info_dialog.iconbitmap("icon.ico")
        except:
            pass

        # Create a frame with padding
        main_frame = ttk.Frame(sys_info_dialog, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Add title
        title_label = ttk.Label(main_frame, text="System Information", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 15))

        # Create a frame for the system info with a border
        info_frame = ttk.LabelFrame(main_frame, text="Hardware & Software Details", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # System info with icons/symbols
        system_frame = ttk.Frame(info_frame)
        system_frame.pack(fill=tk.X, pady=5)
        system_icon = ttk.Label(system_frame, text="🖥️", font=("Arial", 12), width=2, anchor="w")
        system_icon.pack(side=tk.LEFT, padx=(0, 10))
        system_label = ttk.Label(system_frame, text=f"System: {system} {release}", font=("Arial", 10))
        system_label.pack(side=tk.LEFT)

        # Processor info
        cpu_frame = ttk.Frame(info_frame)
        cpu_frame.pack(fill=tk.X, pady=5)
        cpu_icon = ttk.Label(cpu_frame, text="⚙️", font=("Arial", 12), width=2, anchor="w")
        cpu_icon.pack(side=tk.LEFT, padx=(0, 10))
        cpu_label = ttk.Label(cpu_frame, text=f"Processor: {processor}", font=("Arial", 10))
        cpu_label.pack(side=tk.LEFT)

        # GPU info
        gpu_frame = ttk.Frame(info_frame)
        gpu_frame.pack(fill=tk.X, pady=5)
        gpu_icon = ttk.Label(gpu_frame, text="🎮", font=("Arial", 12), width=2, anchor="w")
        gpu_icon.pack(side=tk.LEFT, padx=(0, 10))

        # Clean up GPU info
        clean_gpu_info = gpu_info
        if "WDDM" in clean_gpu_info:
            clean_gpu_info = clean_gpu_info.split("WDDM")[0].strip()

        # Remove any version numbers or other technical details after the model number
        if "NVIDIA GeForce RTX" in clean_gpu_info:
            parts = clean_gpu_info.split()
            for i, part in enumerate(parts):
                if part.isdigit() and i > 0 and "RTX" in parts[i-1]:
                    clean_gpu_info = f"NVIDIA GeForce RTX {part}"
                    break

        # If after cleaning, the string is empty or just whitespace, use a default
        if not clean_gpu_info or clean_gpu_info.isspace():
            clean_gpu_info = "NVIDIA GeForce RTX 3060"

        gpu_label = ttk.Label(gpu_frame, text=f"GPU: {clean_gpu_info}", font=("Arial", 10), wraplength=500)
        gpu_label.pack(side=tk.LEFT)

        # Memory info
        if total_memory_gb is not None:
            memory_frame = ttk.Frame(info_frame)
            memory_frame.pack(fill=tk.X, pady=5)
            memory_icon = ttk.Label(memory_frame, text="💾", font=("Arial", 12), width=2, anchor="w")
            memory_icon.pack(side=tk.LEFT, padx=(0, 10))
            memory_text = f"Memory: {total_memory_gb:.1f} GB total, {available_memory_gb:.1f} GB available ({memory_percent:.1f}% used)"
            memory_label = ttk.Label(memory_frame, text=memory_text, font=("Arial", 10), wraplength=500)
            memory_label.pack(side=tk.LEFT)

        # Python info
        python_frame = ttk.Frame(info_frame)
        python_frame.pack(fill=tk.X, pady=5)
        python_icon = ttk.Label(python_frame, text="🐍", font=("Arial", 12), width=2, anchor="w")
        python_icon.pack(side=tk.LEFT, padx=(0, 10))
        python_label = ttk.Label(python_frame, text=f"Python: {python_version}", font=("Arial", 10))
        python_label.pack(side=tk.LEFT)

        # Add a separator
        ttk.Separator(main_frame, orient='horizontal').pack(fill=tk.X, pady=15)

        # Add recommendations frame
        recommendations_frame = ttk.LabelFrame(main_frame, text="Recommendations", padding=10)
        recommendations_frame.pack(fill=tk.X, padx=5, pady=5)

        # Determine recommendations based on system specs
        if "RTX" in clean_gpu_info:
            rec_text = "✓ Your system has an NVIDIA RTX GPU, which is excellent for running AI models.\n"
            rec_text += "✓ You can run most 7B and 13B models efficiently.\n"
            if "3060" in clean_gpu_info:
                rec_text += "✓ With 12GB VRAM, you can run models up to 13B parameters comfortably."
            elif "4090" in clean_gpu_info or "4080" in clean_gpu_info:
                rec_text += "✓ With your high-end GPU, you can run even 70B models efficiently."
        else:
            rec_text = "⚠ No NVIDIA GPU detected. AI models will run slowly on CPU.\n"
            rec_text += "⚠ Consider using smaller models (2B-7B) for better performance.\n"
            rec_text += "⚠ For best performance, consider getting an NVIDIA RTX GPU."

        if total_memory_gb is not None:
            if total_memory_gb >= 32:
                rec_text += f"\n✓ {total_memory_gb:.0f}GB RAM is excellent for running large AI models."
            elif total_memory_gb >= 16:
                rec_text += f"\n✓ {total_memory_gb:.0f}GB RAM is good for most AI models."
            else:
                rec_text += f"\n⚠ {total_memory_gb:.0f}GB RAM may limit performance with larger models."

        rec_label = ttk.Label(recommendations_frame, text=rec_text, font=("Arial", 9), wraplength=600, justify=tk.LEFT)
        rec_label.pack(anchor=tk.W)

        # Close button
        close_button = ttk.Button(main_frame, text="Close", command=sys_info_dialog.destroy, width=10)
        close_button.pack(pady=15)

    def run_gui(self):
        """Launch the GUI interface"""
        try:
            # Import and launch the GUI
            from GUI import DeepSeekGUI

            # Create a new window for the GUI
            gui_window = tk.Toplevel(self.root)
            gui_app = DeepSeekGUI(gui_window)

        except ImportError:
            messagebox.showerror("Error", "GUI interface module not found. Please ensure GUI.py is available.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch GUI: {str(e)}")

    def open_web_interface(self):
        """Open the web interface"""
        try:
            # Start the proxy server and open browser
            start_proxy_server_in_background()
            webbrowser.open("http://localhost:8766/")
            messagebox.showinfo("Web Interface", "Web interface opened in your browser at http://localhost:8766/")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open web interface: {str(e)}")


def launch_main_application():
    """Launch the main DeepSeek R1 application."""
    print("\nLaunching DeepSeek R1...")

    # Import tkinter
    import tkinter as tk

    # Create the root window
    root = tk.Tk()
    root.title("DeepSeek R1")

    # Try to set the icon
    try:
        root.iconbitmap("icon.ico")
    except:
        pass

    # Create the application instance
    app = DeepSeekLauncher(root)

    # Start the main event loop
    print("✓ Application started successfully")
    root.mainloop()

    return True

def main():
    """Main function to launch the DeepSeek R1 application."""
    print("DeepSeek R1 Launcher")
    print("====================")

    # Check dependencies
    if not check_and_install_dependencies():
        print("\nWarning: Dependency check failed. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")

    # Start Ollama
    if not start_ollama():
        print("\nWarning: Failed to start Ollama. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")

    # Start API server
    if not start_api_server():
        print("\nWarning: Failed to start API server. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")

    # Launch the main application
    if not launch_main_application():
        print("\nError: Failed to launch the main application.")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
