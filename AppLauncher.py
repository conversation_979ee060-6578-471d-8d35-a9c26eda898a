
import tkinter as tk
from tkinter import ttk, messagebox

try:
    from OllamaInstaller import OllamaInstaller
    from OllamaStarter import OllamaStarter
    from ModelDownloader import ModelDownloader
    from ModelStatusChecker import ModelStatusChecker
    from SystemInfo import SystemInfo
    from WebInterfaceLauncher import WebInterfaceLauncher
except ImportError:
    class OllamaInstaller:
        def __init__(self, *_args, **_kwargs): pass
        def install_ollama(self): messagebox.showerror("Error", "OllamaInstaller module not found")
    class OllamaStarter:
        def __init__(self, *_args, **_kwargs): pass
        def start_ollama(self): messagebox.showerror("Error", "OllamaStarter module not found")
    class ModelDownloader:
        def __init__(self, *_args, **_kwargs): pass
        def download_model(self): messagebox.showerror("Error", "ModelDownloader module not found")
    class ModelStatusChecker:
        def __init__(self, *_args, **_kwargs): pass
        def check_model_status(self): messagebox.showerror("Error", "ModelStatusChecker module not found")
    class SystemInfo:
        def __init__(self, *_args, **_kwargs): pass
        def show_system_info(self): messagebox.showerror("Error", "SystemInfo module not found")
    class WebInterfaceLauncher:
        def __init__(self, *_args, **_kwargs): pass
        def open_web_interface(self): messagebox.showerror("Error", "WebInterfaceLauncher module not found")

class DeepSeekLauncher:
    def __init__(self, root):
        self.root = root
        root.title("MO Chat")
        root.geometry("700x550")
        root.minsize(700, 550)

        self.ollama_installer = OllamaInstaller(self.update_status, root)
        self.ollama_starter = OllamaStarter(self.update_status, root)
        self.model_downloader = ModelDownloader(self.update_status, root)
        self.model_status_checker = ModelStatusChecker(self.update_status, root)
        self.system_info_module = SystemInfo(self.update_status, root)
        self.web_interface_launcher = WebInterfaceLauncher(self.update_status, root)

        header = ttk.Frame(root, padding=15)
        header.pack(fill=tk.X)
        ttk.Label(header, text="MO Chat", font=("Arial", 20, "bold")).pack(pady=(0, 5))
        ttk.Separator(root, orient='horizontal').pack(fill=tk.X, padx=15, pady=5)

        main = ttk.Frame(root, padding=10)
        main.pack(fill=tk.BOTH, expand=True)
        buttons = ttk.Frame(main)
        buttons.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        for i in range(2): buttons.columnconfigure(i, weight=1, uniform='column')
        for i in range(4): buttons.rowconfigure(i, minsize=50)

        for (text, cmd, row, col, span) in [
            ("Install Ollama", self.install_ollama, 0, 0, 1),
            ("Start Ollama", self.start_ollama, 0, 1, 1),
            ("Download AI Model", self.download_model, 1, 0, 1),
            ("Check Model Status", self.check_model_status, 1, 1, 1),
            ("System Information", self.system_info, 2, 0, 2),
            ("Open Web Interface", self.open_web_interface, 3, 0, 2)
        ]:
            ttk.Button(buttons, text=text, command=cmd, width=30).grid(row=row, column=col, padx=15, pady=10, sticky="nsew", columnspan=span)

        status_frame = ttk.Frame(root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        ttk.Separator(status_frame, orient='horizontal').pack(fill=tk.X, pady=(5, 2))
        self.status_var = tk.StringVar(value="Ready")
        ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W, padding=(10, 5)).pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

    def update_status(self, message): self.status_var.set(message)
    def install_ollama(self): self.ollama_installer.install_ollama()
    def start_ollama(self): self.ollama_starter.start_ollama()
    def download_model(self, _=None): self.model_downloader.download_model()
    def check_model_status(self): self.model_status_checker.check_model_status()
    def system_info(self): self.system_info_module.show_system_info()
    def open_web_interface(self): self.web_interface_launcher.open_web_interface()


def main():
    root = tk.Tk()
    root.title("DeepSeek R1")
    try: root.iconbitmap("icon.ico")
    except: pass
    DeepSeekLauncher(root)
    root.mainloop()

if __name__ == "__main__": main()
