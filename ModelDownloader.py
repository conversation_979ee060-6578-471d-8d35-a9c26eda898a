"""
ModelDownloader <PERSON><PERSON><PERSON>
Handles downloading AI models with GUI integration
"""

import subprocess
import threading
import time
import requests
import tkinter as tk
from tkinter import ttk, messagebox

try:
    from Ollama import OllamaManager
except ImportError:
    print("Ollama manager module not found, functionality will be limited")
    
    class OllamaManager:
        @staticmethod
        def download_model(_model_name, _callback=None):
            print("Ollama manager module not found, cannot download model")
            return False
        
        @staticmethod
        def get_installed_models():
            return []


class ModelDownloader:
    """Handles AI model downloading functionality"""
    
    def __init__(self, status_callback=None, root=None):
        """
        Initialize the ModelDownloader
        
        Args:
            status_callback: Function to update status messages
            root: Tkinter root window for thread-safe GUI updates
        """
        self.status_callback = status_callback
        self.root = root
        self.available_models = [
            "llama2", "llama2:7b", "llama2:13b", "llama2:70b",
            "mistral", "mistral:7b", "mixtral", "mixtral:8x7b",
            "codellama", "codellama:7b", "codellama:13b", "codellama:34b",
            "phi", "phi:2", "phi:3", "gemma:2b", "gemma:7b",
            "neural-chat", "orca-mini", "vicuna", "wizard-math",
            "deepseek-coder", "deepseek-coder:6.7b", "deepseek-coder:33b",
            "deepseek-llm", "deepseek-llm:7b", "deepseek-llm:67b",
            "qwen", "qwen:7b", "qwen:14b", "qwen:72b",
            "yi", "yi:6b", "yi:34b"
        ]
    
    def check_ollama_installed(self):
        """Check if Ollama is installed"""
        try:
            result = subprocess.run(['ollama', 'list'], 
                                  capture_output=True, 
                                  text=True, 
                                  encoding='utf-8')
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def check_ollama_running(self):
        """Check if Ollama is running"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            return response.status_code == 200
        except:
            return False
    
    def get_installed_models(self):
        """Get list of installed models"""
        installed_models = []
        installed_models_sizes = {}
        
        try:
            # Try API first
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                models_data = response.json().get("models", [])
                for model in models_data:
                    model_name = model.get('name')
                    if model_name:
                        installed_models.append(model_name)
                        size_bytes = model.get('size')
                        if size_bytes and str(size_bytes).isdigit():
                            size_gb = float(size_bytes) / (1024 * 1024 * 1024)
                            installed_models_sizes[model_name] = size_gb
        except:
            pass
        
        # Fallback to command line
        if not installed_models:
            try:
                result = subprocess.run(['ollama', 'list'],
                                      capture_output=True,
                                      text=True,
                                      encoding='utf-8')
                if result.returncode == 0:
                    for line in result.stdout.strip().split('\n'):
                        if line and not line.startswith('NAME'):
                            model_name = line.split()[0].strip()
                            installed_models.append(model_name)
            except:
                pass
        
        return installed_models, installed_models_sizes
    
    def get_model_description(self, model):
        """Get description for a model"""
        if model.startswith("llama2"):
            return "Meta's open source LLM"
        elif model.startswith("mistral"):
            return "High-performance open source LLM"
        elif model.startswith("mixtral"):
            return "Mixture of experts model"
        elif model.startswith("codellama"):
            return "Code-specialized LLM"
        elif model.startswith("phi"):
            return "Microsoft's small but powerful LLM"
        elif model.startswith("gemma"):
            return "Google's lightweight LLM"
        elif model.startswith("neural-chat"):
            return "Optimized for chat applications"
        elif model.startswith("orca-mini"):
            return "Small but capable assistant model"
        elif model.startswith("vicuna"):
            return "Fine-tuned LLaMA model"
        elif model.startswith("wizard"):
            return "Specialized for math problems"
        elif model.startswith("deepseek"):
            return "DeepSeek's powerful LLM"
        elif model.startswith("qwen"):
            return "Alibaba's advanced LLM"
        elif model.startswith("yi"):
            return "01.AI's powerful LLM"
        else:
            return "LLM model"
    
    def get_model_size_info(self, model):
        """Get size information for a model"""
        if ":7b" in model:
            return "(~4.1 GB disk / ~7GB VRAM)"
        elif ":13b" in model:
            return "(~8.2 GB disk / ~13GB VRAM)"
        elif ":34b" in model or ":33b" in model:
            return "(~19.5 GB disk / ~24GB+ VRAM)"
        elif ":70b" in model or ":67b" in model:
            return "(~42 GB disk / ~48GB+ VRAM)"
        elif ":2b" in model or ":3b" in model:
            return "(~1.8 GB disk / ~4GB VRAM)"
        elif ":6b" in model:
            return "(~3.5 GB disk / ~6GB VRAM)"
        elif ":8x7b" in model:
            return "(~15 GB disk / ~24GB+ VRAM)"
        elif ":14b" in model:
            return "(~9 GB disk / ~14GB VRAM)"
        elif ":72b" in model:
            return "(~45 GB disk / ~48GB+ VRAM)"
        else:
            return ""
    
    def download_model(self):
        """Main method to download a model with GUI"""
        # Check prerequisites
        if not self.check_ollama_installed():
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return
        
        # Check if Ollama is running
        if not self.check_ollama_running():
            start_ollama = messagebox.askyesno("Ollama Not Running",
                                            "Ollama is not running. \n\nDo you want to start Ollama now?")
            if start_ollama:
                from OllamaStarter import OllamaStarter
                starter = OllamaStarter(self.status_callback, self.root)
                starter.start_ollama()
                # Wait for Ollama to start
                if self.status_callback:
                    self.status_callback("Waiting for Ollama to start...")
                for _ in range(10):
                    time.sleep(1)
                    if self.check_ollama_running():
                        break
                else:
                    messagebox.showwarning("Warning", "Ollama may not have started properly. The download may fail.")
            else:
                return
        
        # Show model selection dialog
        self._show_model_selection_dialog()
    
    def _show_model_selection_dialog(self):
        """Show the model selection dialog"""
        if self.status_callback:
            self.status_callback("Fetching available models...")
        
        # Get installed models
        installed_models, installed_models_sizes = self.get_installed_models()
        
        # Prepare model list
        available_models = self.available_models.copy()
        if installed_models:
            for model in installed_models:
                if model in available_models:
                    available_models.remove(model)
            available_models = installed_models + available_models
            if self.status_callback:
                self.status_callback(f"Found {len(installed_models)} installed models")
        
        # Create dialog
        self._create_model_dialog(available_models, installed_models, installed_models_sizes)

    def _create_model_dialog(self, available_models, installed_models, installed_models_sizes):
        """Create the model selection dialog"""
        model_dialog = tk.Toplevel(self.root)
        model_dialog.title("Select Model to Download")
        model_dialog.geometry("600x500")
        model_dialog.transient(self.root)
        model_dialog.grab_set()
        model_dialog.resizable(True, True)

        # Center the dialog
        model_dialog.update_idletasks()
        width = model_dialog.winfo_width()
        height = model_dialog.winfo_height()
        x = (model_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (model_dialog.winfo_screenheight() // 2) - (height // 2)
        model_dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # Add labels
        ttk.Label(model_dialog, text="Select a Model to Download", font=("Arial", 12, "bold")).pack(pady=10)
        ttk.Label(model_dialog, text="For your RTX 3060 with 12GB VRAM, any of these models will work well.").pack(pady=5)

        # Create scrollable frame
        container_frame = ttk.Frame(model_dialog)
        container_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        canvas = tk.Canvas(container_frame, height=250, width=550)
        scrollbar = ttk.Scrollbar(container_frame, orient="vertical", command=canvas.yview)
        models_frame = ttk.Frame(canvas)

        models_frame.bind("<Configure>", lambda _: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=models_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # Model selection variable
        selected_model = tk.StringVar(value=available_models[0] if available_models else "llama2")

        # Add model options
        self._add_model_options(models_frame, available_models, installed_models,
                               installed_models_sizes, selected_model)

        # Add custom model entry
        self._add_custom_model_entry(model_dialog, selected_model)

        # Add buttons and handle result
        self._add_dialog_buttons(model_dialog, selected_model)

    def _add_model_options(self, parent, available_models, installed_models, _installed_models_sizes, selected_model):
        """Add model radio button options"""
        for model in available_models:
            description = self.get_model_description(model)
            size_info = self.get_model_size_info(model)
            is_installed = model in installed_models

            # Create label text
            if is_installed and size_info:
                label_text = f"{model} - {description} {size_info} [INSTALLED]"
            elif is_installed:
                label_text = f"{model} - {description} [INSTALLED]"
            elif size_info:
                label_text = f"{model} - {description} {size_info}"
            else:
                label_text = f"{model} - {description}"

            # Create frame for each model
            model_frame = ttk.Frame(parent)
            model_frame.pack(fill=tk.X, pady=2)

            # Add indicator for installed models
            if is_installed:
                indicator = ttk.Label(model_frame, text="✓", foreground="green", font=("Arial", 10, "bold"))
                indicator.pack(side=tk.LEFT, padx=(0, 5))
                label_text = f"{model} - {description}"
                if size_info:
                    label_text += f" {size_info}"

            # Create radio button
            radio_btn = ttk.Radiobutton(model_frame, text=label_text, value=model, variable=selected_model)
            radio_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)

    def _add_custom_model_entry(self, dialog, _selected_model):
        """Add custom model entry field"""
        ttk.Separator(dialog, orient='horizontal').pack(fill=tk.X, padx=20, pady=10)

        custom_frame = ttk.Frame(dialog)
        custom_frame.pack(fill=tk.X, padx=20, pady=5)

        ttk.Label(custom_frame, text="Or enter a custom model name:").pack(anchor=tk.W)

        self.custom_model = tk.StringVar()
        custom_entry = ttk.Entry(custom_frame, textvariable=self.custom_model, width=40)
        custom_entry.pack(fill=tk.X, pady=5)

    def _add_dialog_buttons(self, dialog, selected_model):
        """Add dialog buttons and handle result"""
        buttons_frame = ttk.Frame(dialog)
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        self.dialog_result = {"model": None}

        def on_download():
            if self.custom_model.get().strip():
                self.dialog_result["model"] = self.custom_model.get().strip()
            else:
                self.dialog_result["model"] = selected_model.get()
            dialog.destroy()

        def on_cancel():
            dialog.destroy()

        download_button = ttk.Button(buttons_frame, text="Download", command=on_download, width=15)
        cancel_button = ttk.Button(buttons_frame, text="Cancel", command=on_cancel, width=15)

        cancel_button.pack(side=tk.RIGHT, padx=5)
        download_button.pack(side=tk.RIGHT, padx=5)

        # Wait for dialog result
        self.root.wait_window(dialog)

        # Process result
        if self.dialog_result["model"]:
            self._start_download(self.dialog_result["model"])

    def _start_download(self, model_name):
        """Start the model download process"""
        # Create display name
        if ':' in model_name:
            parts = model_name.split(':')
            base_name = parts[0].replace('-', ' ').title()
            version = parts[1].upper() if parts[1].lower() in ['7b', '13b', '34b', '70b'] else parts[1]
            model_display_name = f"{base_name} {version}"
        else:
            model_display_name = model_name.replace('-', ' ').title()

        if self.status_callback:
            self.status_callback(f"Starting download of {model_display_name}...")

        # Start download in background
        def download_thread():
            success = OllamaManager.download_model(model_name, callback=self._download_progress_callback)
            if self.root:
                if success:
                    self.root.after(0, lambda: self._on_download_success(model_display_name))
                else:
                    self.root.after(0, lambda: self._on_download_failure(model_name))

        import threading
        threading.Thread(target=download_thread, daemon=True).start()

    def _download_progress_callback(self, message):
        """Callback for download progress"""
        print(f"Download progress: {message}")

    def _on_download_success(self, model_display_name):
        """Handle successful download"""
        if self.status_callback:
            self.status_callback(f"{model_display_name} downloaded successfully")
        messagebox.showinfo("Download Complete", f"{model_display_name} downloaded successfully!")

    def _on_download_failure(self, model_name):
        """Handle download failure"""
        if self.status_callback:
            self.status_callback("Download failed")
        messagebox.showerror("Download Failed", f"Failed to download model '{model_name}'.")


def main():
    """Main function for testing the module"""
    downloader = ModelDownloader()
    
    print("Checking Ollama status...")
    if not downloader.check_ollama_installed():
        print("✗ Ollama is not installed")
        return
    
    if not downloader.check_ollama_running():
        print("✗ Ollama is not running")
        return
    
    print("✓ Ollama is ready")
    
    installed, _ = downloader.get_installed_models()
    print(f"Installed models: {len(installed)}")
    for model in installed:
        print(f"  - {model}")


if __name__ == "__main__":
    main()
