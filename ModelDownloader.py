"""
ModelDownloader <PERSON><PERSON><PERSON>
Handles downloading AI models with GUI integration
"""

import subprocess
import threading
import time
import requests
import tkinter as tk
from tkinter import ttk, messagebox

try:
    from Ollama import OllamaManager
except ImportError:
    print("Ollama manager module not found, functionality will be limited")
    
    class OllamaManager:
        @staticmethod
        def download_model(model_name, callback=None):
            print("Ollama manager module not found, cannot download model")
            return False
        
        @staticmethod
        def get_installed_models():
            return []


class ModelDownloader:
    """Handles AI model downloading functionality"""
    
    def __init__(self, status_callback=None, root=None):
        """
        Initialize the ModelDownloader
        
        Args:
            status_callback: Function to update status messages
            root: Tkinter root window for thread-safe GUI updates
        """
        self.status_callback = status_callback
        self.root = root
        self.available_models = [
            "llama2", "llama2:7b", "llama2:13b", "llama2:70b",
            "mistral", "mistral:7b", "mixtral", "mixtral:8x7b",
            "codellama", "codellama:7b", "codellama:13b", "codellama:34b",
            "phi", "phi:2", "phi:3", "gemma:2b", "gemma:7b",
            "neural-chat", "orca-mini", "vicuna", "wizard-math",
            "deepseek-coder", "deepseek-coder:6.7b", "deepseek-coder:33b",
            "deepseek-llm", "deepseek-llm:7b", "deepseek-llm:67b",
            "qwen", "qwen:7b", "qwen:14b", "qwen:72b",
            "yi", "yi:6b", "yi:34b"
        ]
    
    def check_ollama_installed(self):
        """Check if Ollama is installed"""
        try:
            result = subprocess.run(['ollama', 'list'], 
                                  capture_output=True, 
                                  text=True, 
                                  encoding='utf-8')
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def check_ollama_running(self):
        """Check if Ollama is running"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            return response.status_code == 200
        except:
            return False
    
    def get_installed_models(self):
        """Get list of installed models"""
        installed_models = []
        installed_models_sizes = {}
        
        try:
            # Try API first
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                models_data = response.json().get("models", [])
                for model in models_data:
                    model_name = model.get('name')
                    if model_name:
                        installed_models.append(model_name)
                        size_bytes = model.get('size')
                        if size_bytes and str(size_bytes).isdigit():
                            size_gb = float(size_bytes) / (1024 * 1024 * 1024)
                            installed_models_sizes[model_name] = size_gb
        except:
            pass
        
        # Fallback to command line
        if not installed_models:
            try:
                result = subprocess.run(['ollama', 'list'],
                                      capture_output=True,
                                      text=True,
                                      encoding='utf-8')
                if result.returncode == 0:
                    for line in result.stdout.strip().split('\n'):
                        if line and not line.startswith('NAME'):
                            model_name = line.split()[0].strip()
                            installed_models.append(model_name)
            except:
                pass
        
        return installed_models, installed_models_sizes
    
    def get_model_description(self, model):
        """Get description for a model"""
        if model.startswith("llama2"):
            return "Meta's open source LLM"
        elif model.startswith("mistral"):
            return "High-performance open source LLM"
        elif model.startswith("mixtral"):
            return "Mixture of experts model"
        elif model.startswith("codellama"):
            return "Code-specialized LLM"
        elif model.startswith("phi"):
            return "Microsoft's small but powerful LLM"
        elif model.startswith("gemma"):
            return "Google's lightweight LLM"
        elif model.startswith("neural-chat"):
            return "Optimized for chat applications"
        elif model.startswith("orca-mini"):
            return "Small but capable assistant model"
        elif model.startswith("vicuna"):
            return "Fine-tuned LLaMA model"
        elif model.startswith("wizard"):
            return "Specialized for math problems"
        elif model.startswith("deepseek"):
            return "DeepSeek's powerful LLM"
        elif model.startswith("qwen"):
            return "Alibaba's advanced LLM"
        elif model.startswith("yi"):
            return "01.AI's powerful LLM"
        else:
            return "LLM model"
    
    def get_model_size_info(self, model):
        """Get size information for a model"""
        if ":7b" in model:
            return "(~4.1 GB disk / ~7GB VRAM)"
        elif ":13b" in model:
            return "(~8.2 GB disk / ~13GB VRAM)"
        elif ":34b" in model or ":33b" in model:
            return "(~19.5 GB disk / ~24GB+ VRAM)"
        elif ":70b" in model or ":67b" in model:
            return "(~42 GB disk / ~48GB+ VRAM)"
        elif ":2b" in model or ":3b" in model:
            return "(~1.8 GB disk / ~4GB VRAM)"
        elif ":6b" in model:
            return "(~3.5 GB disk / ~6GB VRAM)"
        elif ":8x7b" in model:
            return "(~15 GB disk / ~24GB+ VRAM)"
        elif ":14b" in model:
            return "(~9 GB disk / ~14GB VRAM)"
        elif ":72b" in model:
            return "(~45 GB disk / ~48GB+ VRAM)"
        else:
            return ""
    
    def download_model(self):
        """Main method to download a model with GUI"""
        # Check prerequisites
        if not self.check_ollama_installed():
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return
        
        # Check if Ollama is running
        if not self.check_ollama_running():
            start_ollama = messagebox.askyesno("Ollama Not Running",
                                            "Ollama is not running. \n\nDo you want to start Ollama now?")
            if start_ollama:
                from OllamaStarter import OllamaStarter
                starter = OllamaStarter(self.status_callback, self.root)
                starter.start_ollama()
                # Wait for Ollama to start
                if self.status_callback:
                    self.status_callback("Waiting for Ollama to start...")
                for _ in range(10):
                    time.sleep(1)
                    if self.check_ollama_running():
                        break
                else:
                    messagebox.showwarning("Warning", "Ollama may not have started properly. The download may fail.")
            else:
                return
        
        # Show model selection dialog
        self._show_model_selection_dialog()
    
    def _show_model_selection_dialog(self):
        """Show the model selection dialog"""
        if self.status_callback:
            self.status_callback("Fetching available models...")
        
        # Get installed models
        installed_models, installed_models_sizes = self.get_installed_models()
        
        # Prepare model list
        available_models = self.available_models.copy()
        if installed_models:
            for model in installed_models:
                if model in available_models:
                    available_models.remove(model)
            available_models = installed_models + available_models
            if self.status_callback:
                self.status_callback(f"Found {len(installed_models)} installed models")
        
        # Create dialog
        self._create_model_dialog(available_models, installed_models, installed_models_sizes)


def main():
    """Main function for testing the module"""
    downloader = ModelDownloader()
    
    print("Checking Ollama status...")
    if not downloader.check_ollama_installed():
        print("✗ Ollama is not installed")
        return
    
    if not downloader.check_ollama_running():
        print("✗ Ollama is not running")
        return
    
    print("✓ Ollama is ready")
    
    installed, _ = downloader.get_installed_models()
    print(f"Installed models: {len(installed)}")
    for model in installed:
        print(f"  - {model}")


if __name__ == "__main__":
    main()
