import webbrowser, threading, time, requests
from tkinter import messagebox

try:
    from Ollama import start_proxy_server_in_background
except ImportError:
    def start_proxy_server_in_background(_=8766): return None

class WebInterfaceLauncher:
    def __init__(self, status_callback=None, root=None):
        self.status_callback, self.root, self.proxy_port = status_callback, root, 8766
        self.proxy_url = f"http://localhost:{self.proxy_port}/"

    def check_proxy_running(self):
        try: return requests.get(self.proxy_url, timeout=2).status_code == 200
        except: return False

    def start_proxy_server(self):
        try:
            if self.status_callback: self.status_callback("Starting proxy server...")
            if not start_proxy_server_in_background(self.proxy_port): return False
            return any(time.sleep(1) or self.check_proxy_running() for _ in range(10))
        except: return False

    def open_web_interface(self):
        if self.status_callback: self.status_callback("Opening web interface...")
        if not self.check_proxy_running() and not self.start_proxy_server():
            messagebox.showerror("Error", "Failed to start proxy server.")
            return
        if self.status_callback: self.status_callback("Web interface opened")

    def get_interface_status(self):
        return {'proxy_running': self.check_proxy_running(), 'proxy_url': self.proxy_url, 'proxy_port': self.proxy_port}

    @staticmethod
    def get_quick_status(): return WebInterfaceLauncher().get_interface_status()

def main():
    s = WebInterfaceLauncher().get_interface_status()
    print(f"Proxy: {s['proxy_running']} | URL: {s['proxy_url']} | Port: {s['proxy_port']}")

if __name__ == "__main__": main()
