import webbrowser, threading, time, requests
from tkinter import messagebox

try:
    from Ollama import start_proxy_server_in_background
except ImportError:
    def start_proxy_server_in_background(_=8766): return None

class WebInterfaceLauncher:
    def __init__(self, status_callback=None, root=None):
        self.status_callback, self.root, self.proxy_port = status_callback, root, 8766
        self.proxy_url = f"http://localhost:{self.proxy_port}/"

    def check_proxy_running(self):
        try: return requests.get(self.proxy_url, timeout=2).status_code == 200
        except: return False

    def start_proxy_server(self):
        try:
            if self.status_callback: self.status_callback("Starting proxy server...")
            server_thread = start_proxy_server_in_background(self.proxy_port)
            if not server_thread: return False
            for _ in range(10):
                time.sleep(1)
                if self.check_proxy_running(): return True
            return False
        except: return False

    def open_web_interface(self):
        if self.status_callback: self.status_callback("Opening web interface...")
        if not self.check_proxy_running() and not self.start_proxy_server():
            messagebox.showerror("Error", "Failed to start proxy server. Please check if Ollama is properly installed.")
            if self.status_callback: self.status_callback("Failed to start proxy server")
            return

        def open_browser():
            try:
                time.sleep(1)
                webbrowser.open(self.proxy_url)
                (self.root.after(0, self._on_open_success) if self.root else self._on_open_success())
            except Exception as e:
                (self.root.after(0, lambda: self._on_open_error(str(e))) if self.root else self._on_open_error(str(e)))

        threading.Thread(target=open_browser, daemon=True).start()

    def _on_open_success(self):
        if self.status_callback: self.status_callback("Web interface opened successfully")
        messagebox.showinfo("Web Interface", f"Web interface opened in your browser at {self.proxy_url}")

    def _on_open_error(self, error_msg):
        if self.status_callback: self.status_callback("Failed to open web interface")
        messagebox.showerror("Error", f"Failed to open web interface: {error_msg}")

    def get_interface_status(self):
        return {'proxy_running': self.check_proxy_running(), 'proxy_url': self.proxy_url, 'proxy_port': self.proxy_port, 'description': 'Web-based chat interface'}

    @staticmethod
    def get_quick_status():
        return WebInterfaceLauncher().get_interface_status()

def main():
    launcher = WebInterfaceLauncher()
    status = launcher.get_interface_status()
    print(f"Proxy running: {status['proxy_running']}\nProxy URL: {status['proxy_url']}\nProxy port: {status['proxy_port']}\nDescription: {status['description']}")
    print("Proxy server is running and ready!" if status['proxy_running'] else "Proxy server is not running. Use launcher.open_web_interface() to start it.")

if __name__ == "__main__": main()
