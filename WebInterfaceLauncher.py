"""
WebInterfaceLauncher <PERSON><PERSON><PERSON>les launching the web interface with proxy server
"""

import webbrowser
import threading
import time
import requests
from tkinter import messagebox

try:
    from Ollama import start_proxy_server_in_background
except ImportError:
    print("Ollama proxy server module not found, functionality will be limited")
    
    def start_proxy_server_in_background(port=8766):
        print("Ollama proxy server module not found, skipping")
        return None


class WebInterfaceLauncher:
    """Handles web interface launching functionality"""
    
    def __init__(self, status_callback=None, root=None):
        """
        Initialize the WebInterfaceLauncher
        
        Args:
            status_callback: Function to update status messages
            root: Tkinter root window for thread-safe GUI updates
        """
        self.status_callback = status_callback
        self.root = root
        self.proxy_port = 8766
        self.proxy_url = f"http://localhost:{self.proxy_port}/"
    
    def check_proxy_running(self):
        """
        Check if the proxy server is running
        
        Returns:
            bool: True if proxy server is responding
        """
        try:
            response = requests.get(self.proxy_url, timeout=2)
            return response.status_code == 200
        except:
            return False
    
    def start_proxy_server(self):
        """
        Start the proxy server
        
        Returns:
            bool: True if server started successfully
        """
        try:
            if self.status_callback:
                self.status_callback("Starting proxy server...")
            
            # Start the proxy server
            server_thread = start_proxy_server_in_background(self.proxy_port)
            
            if server_thread is None:
                return False
            
            # Wait for server to start
            for _ in range(10):  # Wait up to 10 seconds
                time.sleep(1)
                if self.check_proxy_running():
                    return True
            
            return False
            
        except Exception as e:
            print(f"Error starting proxy server: {e}")
            return False
    
    def open_web_interface(self):
        """Open the web interface in browser"""
        if self.status_callback:
            self.status_callback("Opening web interface...")
        
        # Check if proxy server is already running
        if not self.check_proxy_running():
            # Try to start the proxy server
            if not self.start_proxy_server():
                messagebox.showerror("Error", 
                                   "Failed to start proxy server. Please check if Ollama is properly installed.")
                if self.status_callback:
                    self.status_callback("Failed to start proxy server")
                return
        
        def open_browser_thread():
            """Background thread for opening browser"""
            try:
                # Wait a moment for server to be fully ready
                time.sleep(1)
                
                # Open browser
                webbrowser.open(self.proxy_url)
                
                if self.root:
                    self.root.after(0, lambda: self._on_open_success())
                else:
                    self._on_open_success()
                    
            except Exception as e:
                if self.root:
                    self.root.after(0, lambda: self._on_open_error(str(e)))
                else:
                    self._on_open_error(str(e))
        
        # Start browser opening in background
        threading.Thread(target=open_browser_thread, daemon=True).start()
    
    def _on_open_success(self):
        """Handle successful web interface opening"""
        if self.status_callback:
            self.status_callback("Web interface opened successfully")
        messagebox.showinfo("Web Interface", 
                          f"Web interface opened in your browser at {self.proxy_url}")
    
    def _on_open_error(self, error_msg):
        """Handle web interface opening error"""
        if self.status_callback:
            self.status_callback("Failed to open web interface")
        messagebox.showerror("Error", f"Failed to open web interface: {error_msg}")
    
    def get_interface_status(self):
        """
        Get web interface status
        
        Returns:
            dict: Interface status information
        """
        return {
            'proxy_running': self.check_proxy_running(),
            'proxy_url': self.proxy_url,
            'proxy_port': self.proxy_port,
            'description': 'Web-based chat interface'
        }
    
    @staticmethod
    def get_quick_status():
        """Get quick web interface status"""
        launcher = WebInterfaceLauncher()
        return launcher.get_interface_status()


def main():
    """Main function for testing the module"""
    launcher = WebInterfaceLauncher()
    
    print("Checking web interface status...")
    status = launcher.get_interface_status()
    
    print(f"Proxy running: {status['proxy_running']}")
    print(f"Proxy URL: {status['proxy_url']}")
    print(f"Proxy port: {status['proxy_port']}")
    print(f"Description: {status['description']}")
    
    if not status['proxy_running']:
        print("Proxy server is not running. Use launcher.open_web_interface() to start it.")
    else:
        print("Proxy server is running and ready!")


if __name__ == "__main__":
    main()
