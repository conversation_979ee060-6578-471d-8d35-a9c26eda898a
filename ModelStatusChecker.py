import subprocess, requests, tkinter as tk
from tkinter import ttk, messagebox

try:
    from Ollama import OllamaManager
except ImportError:
    class OllamaManager:
        @staticmethod
        def get_installed_models(): return []

class ModelStatusChecker:
    def __init__(self, status_callback=None, root=None):
        self.status_callback, self.root = status_callback, root

    def check_ollama_installed(self):
        try: return subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8').returncode == 0
        except: return False

    def check_ollama_running(self):
        try: return requests.get("http://localhost:11434/api/tags", timeout=2).status_code == 200
        except: return False

    def get_installed_models_api(self):
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                return [{'name': m.get('name'), 'size': float(m.get('size')) / (1024**3) if m.get('size') and str(m.get('size')).isdigit() else None, 'modified': m.get('modified_at', 'Unknown')} for m in response.json().get("models", []) if m.get('name')]
        except: pass
        return []

    def get_installed_models_cli(self):
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                return [{'name': parts[0], 'size': float(parts[1].replace('GB', '').strip()) if 'GB' in parts[1] else None, 'modified': parts[2] if len(parts) > 2 else 'Unknown'} for line in result.stdout.strip().split('\n')[1:] if line.strip() and len(parts := line.split()) >= 2]
        except: pass
        return []

    def check_model_status(self):
        if not self.check_ollama_installed():
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return
        if self.status_callback: self.status_callback("Checking model status...")

        if self.check_ollama_running():
            models = self.get_installed_models_api()
            if self.status_callback: self.status_callback("Connected to Ollama API")
        else:
            models = self.get_installed_models_cli()
            if self.status_callback: self.status_callback("Using command line interface")

        self._show_model_status_dialog(models or [])

    def _show_model_status_dialog(self, models):
        dialog = tk.Toplevel(self.root)
        dialog.title("Model Status")
        dialog.geometry("700x500")
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.resizable(True, True)

        dialog.update_idletasks()
        w, h = dialog.winfo_width(), dialog.winfo_height()
        dialog.geometry(f'{w}x{h}+{(dialog.winfo_screenwidth()//2)-(w//2)}+{(dialog.winfo_screenheight()//2)-(h//2)}')

        title_frame = ttk.Frame(dialog)
        title_frame.pack(fill=tk.X, padx=20, pady=10)
        ttk.Label(title_frame, text="Installed Models Status", font=("Arial", 14, "bold")).pack(side=tk.LEFT)
        ttk.Button(title_frame, text="Refresh", command=lambda: self._refresh_status(dialog)).pack(side=tk.RIGHT)

        self._create_model_treeview(dialog, models)
        self._add_status_summary(dialog, models)
        ttk.Button(dialog, text="Close", command=dialog.destroy, width=15).pack(side=tk.BOTTOM, pady=(15, 0))

    def _create_model_treeview(self, parent, models):
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        tree = ttk.Treeview(frame, columns=('Name', 'Size', 'Modified'), show='headings', height=15)
        for col, text, width in [('Name', 'Model Name', 300), ('Size', 'Size (GB)', 100), ('Modified', 'Last Modified', 200)]:
            tree.heading(col, text=text)
            tree.column(col, width=width)

        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        for model in (models or [{"name": "No models found", "size": None, "modified": ""}]):
            tree.insert('', tk.END, values=(model['name'], f"{model['size']:.2f}" if model['size'] else "Unknown", model['modified']))

    def _add_status_summary(self, parent, models):
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.X, padx=20, pady=10)
        total_size = sum(m['size'] for m in models if m['size'])
        summary = f"Total Models: {len(models)}"
        if total_size > 0: summary += f" | Total Size: {total_size:.2f} GB"
        summary += f" | Ollama Status: {'Running' if self.check_ollama_running() else 'Not Running'}"
        ttk.Label(frame, text=summary, font=("Arial", 10)).pack()

    def _refresh_status(self, dialog):
        dialog.destroy()
        self.check_model_status()

    @staticmethod
    def get_quick_status():
        checker = ModelStatusChecker()
        models = checker.get_installed_models_api() if checker.check_ollama_running() else checker.get_installed_models_cli() if checker.check_ollama_installed() else []
        return {'ollama_installed': checker.check_ollama_installed(), 'ollama_running': checker.check_ollama_running(), 'models_count': len(models), 'total_size': sum(m['size'] for m in models if m['size'])}

def main():
    status = ModelStatusChecker.get_quick_status()
    print(f"Ollama installed: {status['ollama_installed']} | Running: {status['ollama_running']} | Models: {status['models_count']} | Size: {status['total_size']:.2f} GB")

if __name__ == "__main__": main()
