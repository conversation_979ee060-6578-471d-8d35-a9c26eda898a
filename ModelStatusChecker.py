"""
ModelStatusChecker Module
Handles checking the status of installed AI models
"""

import subprocess
import requests
import tkinter as tk
from tkinter import ttk, messagebox

try:
    from Ollama import OllamaManager
except ImportError:
    print("Ollama manager module not found, functionality will be limited")
    
    class OllamaManager:
        @staticmethod
        def get_installed_models():
            return []


class ModelStatusChecker:
    """<PERSON>les checking AI model status functionality"""
    
    def __init__(self, status_callback=None, root=None):
        """
        Initialize the ModelStatusChecker
        
        Args:
            status_callback: Function to update status messages
            root: Tkinter root window for thread-safe GUI updates
        """
        self.status_callback = status_callback
        self.root = root
    
    def check_ollama_installed(self):
        """Check if Ollama is installed"""
        try:
            result = subprocess.run(['ollama', 'list'], 
                                  capture_output=True, 
                                  text=True, 
                                  encoding='utf-8')
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def check_ollama_running(self):
        """Check if Ollama is running"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            return response.status_code == 200
        except:
            return False
    
    def get_installed_models_api(self):
        """Get installed models via API"""
        installed_models = []
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                models_data = response.json().get("models", [])
                for model in models_data:
                    model_name = model.get('name')
                    if model_name:
                        size_bytes = model.get('size')
                        size_gb = None
                        if size_bytes and str(size_bytes).isdigit():
                            size_gb = float(size_bytes) / (1024 * 1024 * 1024)
                        
                        installed_models.append({
                            'name': model_name,
                            'size': size_gb,
                            'modified': model.get('modified_at', 'Unknown')
                        })
        except Exception as e:
            print(f"Error getting models via API: {e}")
        
        return installed_models
    
    def get_installed_models_cli(self):
        """Get installed models via command line"""
        installed_models = []
        try:
            result = subprocess.run(['ollama', 'list'], 
                                  capture_output=True, 
                                  text=True, 
                                  encoding='utf-8')
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines[1:]:  # Skip header
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 2:
                            model_name = parts[0]
                            size_str = parts[1] if len(parts) > 1 else "Unknown"
                            size_gb = None
                            if 'GB' in size_str:
                                try:
                                    size_gb = float(size_str.replace('GB', '').strip())
                                except:
                                    pass
                            
                            installed_models.append({
                                'name': model_name,
                                'size': size_gb,
                                'modified': parts[2] if len(parts) > 2 else 'Unknown'
                            })
        except Exception as e:
            print(f"Error getting models via CLI: {e}")
        
        return installed_models
    
    def check_model_status(self):
        """Main method to check model status with GUI"""
        # Check prerequisites
        if not self.check_ollama_installed():
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return
        
        if self.status_callback:
            self.status_callback("Checking model status...")
        
        # Get installed models
        installed_models = []
        
        # Try API first if Ollama is running
        if self.check_ollama_running():
            installed_models = self.get_installed_models_api()
            if self.status_callback:
                self.status_callback("Connected to Ollama API")
        
        # Fallback to CLI if API failed or Ollama not running
        if not installed_models:
            installed_models = self.get_installed_models_cli()
            if self.status_callback:
                self.status_callback("Using command line interface")
        
        # Show results
        self._show_model_status_dialog(installed_models)
    
    def _show_model_status_dialog(self, installed_models):
        """Show the model status dialog"""
        # Create dialog
        status_dialog = tk.Toplevel(self.root)
        status_dialog.title("Model Status")
        status_dialog.geometry("700x500")
        status_dialog.transient(self.root)
        status_dialog.grab_set()
        status_dialog.resizable(True, True)
        
        # Center the dialog
        status_dialog.update_idletasks()
        width = status_dialog.winfo_width()
        height = status_dialog.winfo_height()
        x = (status_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (status_dialog.winfo_screenheight() // 2) - (height // 2)
        status_dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
        # Add title
        title_frame = ttk.Frame(status_dialog)
        title_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Label(title_frame, text="Installed Models Status", 
                 font=("Arial", 14, "bold")).pack(side=tk.LEFT)
        
        # Add refresh button
        refresh_btn = ttk.Button(title_frame, text="Refresh", 
                               command=lambda: self._refresh_status(status_dialog))
        refresh_btn.pack(side=tk.RIGHT)
        
        # Create treeview for model list
        self._create_model_treeview(status_dialog, installed_models)
        
        # Add summary
        self._add_status_summary(status_dialog, installed_models)
        
        # Add close button
        close_btn = ttk.Button(status_dialog, text="Close", 
                             command=status_dialog.destroy, width=15)
        close_btn.pack(pady=10)
    
    def _create_model_treeview(self, parent, installed_models):
        """Create treeview for displaying models"""
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Create treeview
        columns = ('Name', 'Size', 'Modified')
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
        
        # Define headings
        tree.heading('Name', text='Model Name')
        tree.heading('Size', text='Size (GB)')
        tree.heading('Modified', text='Last Modified')
        
        # Configure column widths
        tree.column('Name', width=300)
        tree.column('Size', width=100)
        tree.column('Modified', width=200)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Populate with data
        if installed_models:
            for model in installed_models:
                size_str = f"{model['size']:.2f}" if model['size'] else "Unknown"
                tree.insert('', tk.END, values=(
                    model['name'],
                    size_str,
                    model['modified']
                ))
        else:
            tree.insert('', tk.END, values=("No models found", "", ""))
    
    def _add_status_summary(self, parent, installed_models):
        """Add status summary"""
        summary_frame = ttk.Frame(parent)
        summary_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Calculate total size
        total_size = sum(model['size'] for model in installed_models if model['size'])
        
        # Create summary text
        summary_text = f"Total Models: {len(installed_models)}"
        if total_size > 0:
            summary_text += f" | Total Size: {total_size:.2f} GB"
        
        # Ollama status
        ollama_status = "Running" if self.check_ollama_running() else "Not Running"
        summary_text += f" | Ollama Status: {ollama_status}"
        
        ttk.Label(summary_frame, text=summary_text, font=("Arial", 10)).pack()
    
    def _refresh_status(self, dialog):
        """Refresh the status dialog"""
        dialog.destroy()
        self.check_model_status()
    
    @staticmethod
    def get_quick_status():
        """Get quick status information"""
        checker = ModelStatusChecker()
        
        status = {
            'ollama_installed': checker.check_ollama_installed(),
            'ollama_running': checker.check_ollama_running(),
            'models_count': 0,
            'total_size': 0
        }
        
        if status['ollama_installed']:
            if status['ollama_running']:
                models = checker.get_installed_models_api()
            else:
                models = checker.get_installed_models_cli()
            
            status['models_count'] = len(models)
            status['total_size'] = sum(model['size'] for model in models if model['size'])
        
        return status


def main():
    """Main function for testing the module"""
    checker = ModelStatusChecker()
    
    print("Getting quick status...")
    status = checker.get_quick_status()
    
    print(f"Ollama installed: {status['ollama_installed']}")
    print(f"Ollama running: {status['ollama_running']}")
    print(f"Models count: {status['models_count']}")
    print(f"Total size: {status['total_size']:.2f} GB")


if __name__ == "__main__":
    main()
