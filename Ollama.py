import os
import sys
import subprocess
import time
import json
import platform
import threading
import http.server
import socketserver
import webbrowser
import socket
from urllib.parse import urlparse
from http.server import HTTPServer, BaseHTTPRequestHandler
import requests

API_SERVER_PORT = 8765
PROXY_SERVER_PORT = 8766

class OllamaManager:

    @staticmethod
    def install_ollama():
        print("===================================================")
        print("Installing Ollama")
        print("===================================================")
        print()

        if platform.system() != "Windows":
            print("This script is designed for Windows. For other platforms, please visit:")
            print("https://ollama.com/download")
            return False

        print("Downloading Ollama...")
        try:
            subprocess.run(["curl", "-L", "-o", "OllamaSetup.exe", "https://ollama.com/download/OllamaSetup.exe"], check=True)
            print("\nInstalling Ollama...")
            print("Please follow the installation instructions in the installer window.")
            subprocess.run(["start", "/wait", "OllamaSetup.exe"], shell=True, check=True)
            print("\nStarting Ollama...")
            subprocess.Popen(["ollama", "serve"],
                           creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0)

            print("\n===================================================")
            print("Installation complete.")
            print("===================================================")
            return True
        except Exception as e:
            print(f"Error during installation: {e}")
            return False

    @staticmethod
    def start_ollama():
        print("===================================")
        print("Starting Ollama")
        print("===================================")
        print()
        print("Checking if Ollama is already running...")
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                print("Ollama is already running!")
                return True
        except:
            print("Ollama is not running, starting it now...")

        print("Starting Ollama...")
        try:
            if platform.system() == "Windows":
                subprocess.Popen(["ollama", "serve"],
                               creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0)
            else:
                subprocess.Popen(["ollama", "serve"])
        except Exception as e:
            print(f"Error starting Ollama: {e}")
            return False

        print("Waiting for Ollama to initialize...")
        max_attempts = 15
        for attempt in range(max_attempts):
            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=2)
                if response.status_code == 200:
                    print("\nSUCCESS: Ollama is now running!")
                    print("\n===================================")
                    print("Ollama has been started successfully!")
                    print("===================================")
                    return True
            except:
                pass

            print(f"Waiting for Ollama to start... ({attempt+1}/{max_attempts})")
            time.sleep(2)

        print("\nERROR: Ollama did not start within the timeout period.")
        print("Please check if Ollama is installed correctly.")
        return False

    @staticmethod
    def check_ollama_running():
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            return response.status_code == 200
        except:
            return False

    @staticmethod
    def get_installed_models():
        models = []

        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                data = response.json()
                if "models" in data:
                    return data["models"]
        except:
            pass
        try:
            result = subprocess.run(['ollama', 'list'],
                                  capture_output=True,
                                  text=True,
                                  encoding='utf-8')

            if result.returncode == 0:
                for line in result.stdout.strip().split('\n')[1:]:
                    if line.strip():
                        model_name = line.split()[0]
                        models.append({"name": model_name})
        except:
            pass

        return models

    @staticmethod
    def download_model(model_name, callback=None):
        try:
            if callback:
                callback("Trying approach 1: Direct API call with insecure flag...")

            try:
                response = requests.post(
                    "http://localhost:11434/api/pull",
                    json={"name": model_name, "insecure": True, "stream": False},
                    timeout=600  
                )

                if response.status_code == 200:
                    if callback:
                        callback("Approach 1 successful! Download in progress...")

                    start_time = time.time()
                    last_update = start_time
                    while time.time() - start_time < 600:  
                        try:
                            check_response = requests.get("http://localhost:11434/api/tags", timeout=5)
                            if check_response.status_code == 200:
                                models = check_response.json().get("models", [])
                                if any(m.get("name") == model_name for m in models):
                                    if callback:
                                        callback(f"Model {model_name} successfully downloaded!")
                                    return True
                        except Exception:
                            pass

                        current_time = time.time()
                        if callback and (current_time - last_update) >= 5:
                            elapsed = time.time() - start_time
                            minutes = int(elapsed // 60)
                            seconds = int(elapsed % 60)
                            callback(f"Download in progress via API... ({minutes:02d}:{seconds:02d})")
                            last_update = current_time
                        time.sleep(1)
            except Exception as e:
                if callback:
                    callback(f"Approach 1 failed: {str(e)}")

            if callback:
                callback("Approach 2: Command line with insecure flag...")

            try:
                cmd = ["ollama", "pull", model_name, "--insecure"]

                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,
                    universal_newlines=True
                )

                for line in process.stdout:
                    if callback:
                        callback(line.strip())

                process.wait()

                if process.returncode == 0:
                    if callback:
                        callback(f"Successfully downloaded {model_name}")
                    return True
                else:
                    error = process.stderr.read()
                    if callback:
                        callback(f"Approach 2 failed: {error}")
            except Exception as e:
                if callback:
                    callback(f"Approach 2 failed: {str(e)}")

            if callback:
                callback("Approach 3: Using alternative registry...")

            try:
                env = os.environ.copy()
                env['OLLAMA_HOST'] = 'https://ollama.com'
                result = subprocess.run(
                    ['ollama', 'pull', model_name],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False,
                    timeout=300,  
                    env=env
                )

                if result.returncode == 0:
                    if callback:
                        callback("Approach 3 successful!")
                    return True
                else:
                    if callback:
                        callback(f"Approach 3 failed: {result.stderr}")
            except Exception as e:
                if callback:
                    callback(f"Approach 3 failed: {str(e)}")

            if callback:
                callback("All download approaches failed.")
            return False
        except Exception as e:
            if callback:
                callback(f"Error downloading model: {e}")
            return False

    @staticmethod
    def create_start_ollama_batch():
        batch_content = """@echo off
echo Starting Ollama...
start /b "" ollama serve
echo Ollama started in the background.
echo You can now close this window.
pause
"""

        with open("start_ollama.bat", "w") as f:
            f.write(batch_content)

        print("Created start_ollama.bat")
        return os.path.abspath("start_ollama.bat")


class OllamaAPIHandler(BaseHTTPRequestHandler):
    def _set_headers(self, status_code=200, content_type='application/json'):
        self.send_response(status_code)
        self.send_header('Content-type', content_type)
        self.send_header('Access-Control-Allow-Origin', '*')  
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def do_OPTIONS(self):
        self._set_headers()

    def do_GET(self):
        if self.path == '/status':
            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=2)
                if response.status_code == 200:
                    self._set_headers()
                    self.wfile.write(json.dumps({
                        'status': 'running',
                        'models': response.json().get('models', [])
                    }).encode())
                else:
                    self._set_headers()
                    self.wfile.write(json.dumps({
                        'status': 'error',
                        'message': f'Ollama returned status code {response.status_code}'
                    }).encode())
            except Exception as e:
                self._set_headers()
                self.wfile.write(json.dumps({
                    'status': 'not_running',
                    'message': str(e)
                }).encode())
        else:
            self._set_headers(404)
            self.wfile.write(json.dumps({
                'error': 'Not found'
            }).encode())

    def do_POST(self):
        if self.path == '/start-ollama':
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)

            try:
                json.loads(post_data.decode())
                try:
                    response = requests.get("http://localhost:11434/api/tags", timeout=1)
                    if response.status_code == 200:
                        self._set_headers()
                        self.wfile.write(json.dumps({
                            'success': True,
                            'message': 'Ollama is already running'
                        }).encode())
                        return
                except:
                    pass
                if OllamaManager.start_ollama():
                    self._set_headers()
                    self.wfile.write(json.dumps({
                        'success': True,
                        'message': 'Ollama started successfully'
                    }).encode())
                else:
                    self._set_headers(500)
                    self.wfile.write(json.dumps({
                        'success': False,
                        'message': 'Failed to start Ollama'
                    }).encode())
            except json.JSONDecodeError:
                self._set_headers(400)
                self.wfile.write(json.dumps({
                    'success': False,
                    'message': 'Invalid JSON'
                }).encode())
        else:
            self._set_headers(404)
            self.wfile.write(json.dumps({
                'error': 'Not found'
            }).encode())

def run_api_server(port=API_SERVER_PORT):
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        sock.bind(('', port))
        sock.close()
        port_available = True
    except OSError:
        port_available = False
        print(f"Port {port} is already in use. API server may already be running.")
        return None

    if port_available:
        server_address = ('', port)
        httpd = HTTPServer(server_address, OllamaAPIHandler)
        print(f'Starting Ollama API server on port {port}...')
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("API server stopped.")
        finally:
            httpd.server_close()
    return None

def start_api_server_in_background(port=API_SERVER_PORT):
    server_thread = threading.Thread(target=run_api_server, args=(port,))
    server_thread.daemon = True  
    server_thread.start()
    return server_thread


class OllamaProxyHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        self.send_cors_headers()
        parsed_url = urlparse(self.path)
        if parsed_url.path == '/':
            self.serve_chat_interface()
            return

        if parsed_url.path.startswith('/api/'):
            ollama_path = parsed_url.path[4:]
            ollama_url = f"http://localhost:11434{ollama_path}"

            try:
                response = requests.get(ollama_url, timeout=5)

                self.send_response(response.status_code)
                self.send_cors_headers()
                for header, value in response.headers.items():
                    if header.lower() == 'content-type':
                        self.send_header('Content-Type', value)
                        break
                else:
                    self.send_header('Content-Type', 'application/json')

                self.end_headers()
                self.wfile.write(response.content)

            except Exception as e:
                self.send_response(500)
                self.send_cors_headers()
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({
                    'error': str(e)
                }).encode())

            return
        return http.server.SimpleHTTPRequestHandler.do_GET(self)

    def do_POST(self):
        self.send_cors_headers()
        parsed_url = urlparse(self.path)
        if parsed_url.path.startswith('/api/'):
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            ollama_path = parsed_url.path[4:]  
            ollama_url = f"http://localhost:11434{ollama_path}"

            try:
                content_type = self.headers.get('Content-Type', 'application/json')
                response = requests.post(
                    ollama_url,
                    data=post_data,
                    headers={'Content-Type': content_type},
                    timeout=30
                )

                self.send_response(response.status_code)
                self.send_cors_headers()

                for header, value in response.headers.items():
                    if header.lower() == 'content-type':
                        self.send_header('Content-Type', value)
                        break
                else:
                    self.send_header('Content-Type', 'application/json')

                self.end_headers()
                self.wfile.write(response.content)

            except Exception as e:
                self.send_response(500)
                self.send_cors_headers()
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({
                    'error': str(e)
                }).encode())

            return
        self.send_response(404)
        self.send_cors_headers()
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({
            'error': 'Not found'
        }).encode())

    def do_OPTIONS(self):
        self.send_response(200)
        self.send_cors_headers()
        self.end_headers()

    def send_cors_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')

    def serve_chat_interface(self):
        html_files = [
            'Chat_Interface.html',
            'deepseek_chat_interface.html',
            'web_interface.html',
            'Website.html'
        ]

        for html_file in html_files:
            try:
                if os.path.exists(html_file):
                    with open(html_file, 'rb') as f:
                        content = f.read()

                    self.send_response(200)
                    self.send_header('Content-Type', 'text/html')
                    self.end_headers()
                    self.wfile.write(content)
                    return
            except Exception:
                continue

        self.send_response(404)
        self.send_header('Content-Type', 'text/plain')
        self.end_headers()
        self.wfile.write(b"Error: No chat interface HTML file found")

def run_proxy_server(port=PROXY_SERVER_PORT):
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)

    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        sock.bind(('', port))
        sock.close()
        port_available = True
    except OSError:
        port_available = False
        print(f"Port {port} is already in use. Assuming proxy server is already running.")
        webbrowser.open(f"http://localhost:{port}/")
        return

    if port_available:
        try:
            handler = OllamaProxyHandler
            httpd = socketserver.TCPServer(("", port), handler)

            print(f"Starting Ollama proxy server on port {port}...")
            print(f"Open http://localhost:{port}/ in your browser")

            try:
                webbrowser.open(f"http://localhost:{port}/")
            except Exception as e:
                print(f"Failed to open browser: {e}")

            try:
                httpd.serve_forever()
            except KeyboardInterrupt:
                print("Server stopped.")
            finally:
                httpd.server_close()
        except Exception as e:
            print(f"Error starting server: {e}")
            for html_file in ['Chat_Interface.html', 'deepseek_chat_interface.html', 'web_interface.html', 'Website.html']:
                if os.path.exists(html_file):
                    print(f"Opening {html_file} directly as fallback")
                    webbrowser.open("file://" + os.path.abspath(html_file))
                    break
    else:
        print("Failed to start server, port is in use.")
        for html_file in ['Chat_Interface.html', 'deepseek_chat_interface.html', 'web_interface.html', 'Website.html']:
            if os.path.exists(html_file):
                print(f"Opening {html_file} directly as fallback")
                webbrowser.open("file://" + os.path.abspath(html_file))
                break

def start_proxy_server_in_background(port=PROXY_SERVER_PORT):
    server_thread = threading.Thread(target=run_proxy_server, args=(port,))
    server_thread.daemon = True
    server_thread.start()
    return server_thread

if __name__ == "__main__":
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "install":
            OllamaManager.install_ollama()
            input("\nPress Enter to exit...")
        elif command == "start":
            OllamaManager.start_ollama()
            input("\nPress Enter to exit...")
        elif command == "proxy":
            run_proxy_server()
        elif command == "api":
            run_api_server()
        elif command == "batch":
            OllamaManager.create_start_ollama_batch()
            input("\nPress Enter to exit...")
        else:
            print("Unknown command. Available commands: install, start, proxy, api, batch")
    else:
        OllamaManager.start_ollama()
        input("\nPress Enter to exit...")
