import subprocess, time, platform, webbrowser, requests

class OllamaManager:
    @staticmethod
    def install_ollama():
        if platform.system() != "Windows":
            webbrowser.open("https://ollama.com/download")
            return False
        try:
            subprocess.run(["curl", "-L", "-o", "OllamaSetup.exe", "https://ollama.com/download/OllamaSetup.exe"], check=True)
            subprocess.run(["start", "/wait", "OllamaSetup.exe"], shell=True, check=True)
            subprocess.Popen(["ollama", "serve"], creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0)
            return True
        except: return False

    @staticmethod
    def start_ollama():
        try:
            if requests.get("http://localhost:11434/api/tags", timeout=1).status_code == 200: return True
        except: pass
        try:
            if platform.system() == "Windows":
                subprocess.Popen(["ollama", "serve"], creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0)
            else:
                subprocess.Popen(["ollama", "serve"])
        except: return False
        for _ in range(10):
            try:
                if requests.get("http://localhost:11434/api/tags", timeout=1).status_code == 200: return True
            except: pass
            time.sleep(1)
        return False

    @staticmethod
    def check_ollama_running():
        try: return requests.get("http://localhost:11434/api/tags", timeout=1).status_code == 200
        except: return False

    @staticmethod
    def get_installed_models():
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=1)
            if response.status_code == 200: return response.json().get("models", [])
        except: pass
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                return [{"name": line.split()[0]} for line in result.stdout.strip().split('\n')[1:] if line.strip()]
        except: pass
        return []

    @staticmethod
    def download_model(model_name, callback=None):
        if callback: callback(f"Downloading {model_name}...")
        try:
            process = subprocess.Popen(['ollama', 'pull', model_name], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1, universal_newlines=True)
            for line in process.stdout:
                if callback: callback(line.strip())
            process.wait()
            if process.returncode == 0:
                if callback: callback(f"Successfully downloaded {model_name}")
                return True
            else:
                if callback: callback(f"Download failed: {process.stderr.read()}")
                return False
        except Exception as e:
            if callback: callback(f"Error: {e}")
            return False

def start_api_server_in_background(_port=8765):
    return None

def start_proxy_server_in_background(_port=8766):
    html_files = ['Chat_Interface.html', 'deepseek_chat_interface.html', 'web_interface.html', 'Website.html']
    for html_file in html_files:
        try:
            import os
            if os.path.exists(html_file):
                webbrowser.open("file://" + os.path.abspath(html_file))
                break
        except: pass
    return None

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1].lower() == "start":
        OllamaManager.start_ollama()
    else:
        OllamaManager.start_ollama()
