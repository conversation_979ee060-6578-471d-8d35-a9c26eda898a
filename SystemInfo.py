import sys, platform, subprocess, tkinter as tk
from tkinter import ttk

class SystemInfo:
    def __init__(self, status_callback=None, root=None):
        self.status_callback, self.root = status_callback, root

    def get_system_info(self):
        return {'system': platform.system(), 'release': platform.release(), 'processor': platform.processor(),
                'python_version': sys.version.split()[0], 'memory': self._get_memory_info(), 'gpu': self._get_gpu_info()}

    def _get_memory_info(self):
        try: import psutil; m = psutil.virtual_memory(); return {'total_gb': m.total / (1024**3), 'available_gb': m.available / (1024**3), 'percent': m.percent}
        except: return {'total_gb': None, 'available_gb': None, 'percent': None}

    def _get_gpu_info(self):
        try:
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                for line in result.stdout.strip().split('\n'):
                    if "RTX" in line:
                        parts = line.strip().split()
                        for i, part in enumerate(parts):
                            if "RTX" in part and i+1 < len(parts): return {'detected': True, 'info': f"NVIDIA GeForce {part} {parts[i+1]}", 'nvidia': True}
                return {'detected': True, 'info': "NVIDIA GPU detected", 'nvidia': True}
            return {'detected': False, 'info': "No NVIDIA GPU detected", 'nvidia': False}
        except: return {'detected': False, 'info': "Could not detect GPU", 'nvidia': False}

    def _clean_gpu_info(self, gpu_info): return (gpu_info.replace("NVIDIA GeForce", "").strip() or "RTX 3060")

    def _get_ai_recommendations(self, gpu_info):
        clean = self._clean_gpu_info(gpu_info['info'])
        if "RTX" in clean:
            return "✓ NVIDIA RTX GPU - excellent for AI models.\n✓ Can run most 7B and 13B models efficiently.\n" + ("✓ 12GB VRAM - can run models up to 13B parameters." if "3060" in clean else "✓ High-end GPU - can run even 70B models efficiently." if any(x in clean for x in ["4090", "4080"]) else "✓ Check GPU VRAM for optimal model size.")
        return "⚠ No NVIDIA GPU - AI models will run slowly on CPU.\n⚠ Consider smaller models (2B-7B) for better performance."

    def show_system_info(self):
        if self.status_callback: self.status_callback("Gathering system information...")
        info = self.get_system_info()

        dialog = tk.Toplevel(self.root)
        dialog.title("System Information")
        dialog.geometry("580x450")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        try: dialog.iconbitmap("icon.ico")
        except: pass

        main = ttk.Frame(dialog, padding=5)
        main.pack(fill=tk.BOTH, expand=True)
        ttk.Label(main, text="System Information", font=("Arial", 12, "bold")).pack(pady=(0, 5))

        info_frame = ttk.LabelFrame(main, text="Hardware & Software Details", padding=3)
        info_frame.pack(fill=tk.X, padx=1, pady=1)

        for icon, text in [("🖥️", f"System: {info['system']} {info['release']}"),
                          ("🐍", f"Python: {info['python_version']}"),
                          ("⚙️", f"Processor: {info['processor']}"),
                          ("💾", f"Memory: {info['memory']['total_gb']:.1f} GB total, {info['memory']['available_gb']:.1f} GB available ({info['memory']['percent']:.1f}% used)" if info['memory']['total_gb'] else "Memory: Information not available"),
                          ("🎮", f"Graphics: {self._clean_gpu_info(info['gpu']['info'])}")]:
            row = ttk.Frame(info_frame)
            row.pack(fill=tk.X)
            ttk.Label(row, text=icon, font=("Arial", 10), width=2).pack(side=tk.LEFT)
            ttk.Label(row, text=text, font=("Arial", 9)).pack(side=tk.LEFT)

        rec_frame = ttk.LabelFrame(main, text="AI Model Recommendations", padding=3)
        rec_frame.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)
        ttk.Label(rec_frame, text=self._get_ai_recommendations(info['gpu']), font=("Arial", 8), justify=tk.LEFT, wraplength=520).pack(anchor=tk.W)

        ttk.Button(main, text="Close", command=dialog.destroy, width=12).pack(pady=5)
        if self.status_callback: self.status_callback("System information displayed")

    @staticmethod
    def get_quick_info():
        info = SystemInfo().get_system_info()
        return {'system': f"{info['system']} {info['release']}", 'python': info['python_version'], 'memory_gb': info['memory']['total_gb'] or 0, 'gpu_detected': info['gpu']['detected'], 'nvidia_gpu': info['gpu']['nvidia']}

def main():
    info = SystemInfo.get_quick_info()
    print(f"System: {info['system']} | Python: {info['python']} | Memory: {info['memory_gb']:.1f} GB | GPU: {info['gpu_detected']} | NVIDIA: {info['nvidia_gpu']}")

if __name__ == "__main__": main()
