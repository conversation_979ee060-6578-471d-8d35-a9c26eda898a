import sys, platform, subprocess, tkinter as tk
from tkinter import ttk

class SystemInfo:
    def __init__(self, status_callback=None, root=None):
        self.status_callback, self.root = status_callback, root

    def get_system_info(self):
        return {'system': platform.system(), 'release': platform.release(), 'processor': platform.processor(),
                'python_version': sys.version.split()[0], 'memory': self._get_memory_info(), 'gpu': self._get_gpu_info()}

    def _get_memory_info(self):
        try:
            import psutil
            m = psutil.virtual_memory()
            return {'total_gb': m.total / (1024**3), 'available_gb': m.available / (1024**3), 'percent': m.percent}
        except ImportError:
            return {'total_gb': None, 'available_gb': None, 'percent': None}

    def _get_gpu_info(self):
        try:
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                gpu_info = "NVIDIA GPU detected"
                for line in result.stdout.strip().split('\n'):
                    if "NVIDIA GeForce RTX" in line or "NVIDIA RTX" in line:
                        parts = line.strip().split()
                        for i, part in enumerate(parts):
                            if "RTX" in part and i+1 < len(parts):
                                gpu_info = f"NVIDIA GeForce {part} {parts[i+1]}"
                                break
                        break
                return {'detected': True, 'info': gpu_info, 'nvidia': True}
            return {'detected': False, 'info': "No NVIDIA GPU detected", 'nvidia': False}
        except:
            return {'detected': False, 'info': "Could not detect GPU", 'nvidia': False}

    def _clean_gpu_info(self, gpu_info):
        clean = gpu_info.replace("NVIDIA GeForce", "").strip()
        return clean if clean and not clean.isspace() else "NVIDIA GeForce RTX 3060"

    def _get_ai_recommendations(self, gpu_info):
        clean = self._clean_gpu_info(gpu_info['info'])
        if "RTX" in clean:
            base = "✓ Your system has an NVIDIA RTX GPU, excellent for AI models.\n✓ You can run most 7B and 13B models efficiently.\n"
            if "3060" in clean: return base + "✓ With 12GB VRAM, you can run models up to 13B parameters comfortably."
            elif "4090" in clean or "4080" in clean: return base + "✓ With your high-end GPU, you can run even 70B models efficiently."
            else: return base + "✓ Check your GPU's VRAM for optimal model size selection."
        return "⚠ No NVIDIA GPU detected. AI models will run slowly on CPU.\n⚠ Consider using smaller models (2B-7B) for better performance.\n⚠ For best performance, consider getting an NVIDIA RTX GPU."

    def show_system_info(self):
        if self.status_callback: self.status_callback("Gathering system information...")
        info = self.get_system_info()

        dialog = tk.Toplevel(self.root)
        dialog.title("System Information")
        dialog.geometry("650x600")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        try: dialog.iconbitmap("icon.ico")
        except: pass

        main = ttk.Frame(dialog, padding=15)
        main.pack(fill=tk.BOTH, expand=True)
        ttk.Label(main, text="System Information", font=("Arial", 14, "bold")).pack(pady=(0, 15))

        info_frame = ttk.LabelFrame(main, text="Hardware & Software Details", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self._add_info_row(info_frame, "🖥️", f"System: {info['system']} {info['release']}")
        self._add_info_row(info_frame, "🐍", f"Python: {info['python_version']}")
        self._add_info_row(info_frame, "⚙️", f"Processor: {info['processor']}")

        if info['memory']['total_gb']:
            mem = f"Memory: {info['memory']['total_gb']:.1f} GB total, {info['memory']['available_gb']:.1f} GB available ({info['memory']['percent']:.1f}% used)"
            self._add_info_row(info_frame, "💾", mem)
        else:
            self._add_info_row(info_frame, "💾", "Memory: Information not available")

        self._add_info_row(info_frame, "🎮", f"Graphics: {self._clean_gpu_info(info['gpu']['info'])}")

        rec_frame = ttk.LabelFrame(main, text="AI Model Recommendations", padding=10)
        rec_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        ttk.Label(rec_frame, text=self._get_ai_recommendations(info['gpu']), font=("Arial", 9), justify=tk.LEFT, wraplength=580).pack(anchor=tk.W)

        ttk.Button(main, text="Close", command=dialog.destroy, width=15).pack(pady=(15, 0))
        if self.status_callback: self.status_callback("System information displayed")

    def _add_info_row(self, parent, icon, text):
        row = ttk.Frame(parent)
        row.pack(fill=tk.X, pady=5)
        ttk.Label(row, text=icon, font=("Arial", 12), width=2, anchor="w").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Label(row, text=text, font=("Arial", 10)).pack(side=tk.LEFT)

    @staticmethod
    def get_quick_info():
        info = SystemInfo().get_system_info()
        return {'system': f"{info['system']} {info['release']}", 'python': info['python_version'],
                'memory_gb': info['memory']['total_gb'] or 0, 'gpu_detected': info['gpu']['detected'], 'nvidia_gpu': info['gpu']['nvidia']}

def main():
    info = SystemInfo.get_quick_info()
    print(f"System: {info['system']} | Python: {info['python']} | Memory: {info['memory_gb']:.1f} GB | GPU: {info['gpu_detected']} | NVIDIA: {info['nvidia_gpu']}")

if __name__ == "__main__": main()
