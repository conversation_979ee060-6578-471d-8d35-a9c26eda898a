"""
SystemInfo Module
Handles system information display with GUI integration
"""

import sys
import platform
import subprocess
import tkinter as tk
from tkinter import ttk


class SystemInfo:
    """Handles system information functionality"""
    
    def __init__(self, status_callback=None, root=None):
        """
        Initialize the SystemInfo
        
        Args:
            status_callback: Function to update status messages
            root: Tkinter root window for thread-safe GUI updates
        """
        self.status_callback = status_callback
        self.root = root
    
    def get_system_info(self):
        """
        Gather comprehensive system information
        
        Returns:
            dict: System information
        """
        info = {
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
            'processor': platform.processor(),
            'python_version': sys.version.split()[0],
            'memory': self._get_memory_info(),
            'gpu': self._get_gpu_info()
        }
        
        return info
    
    def _get_memory_info(self):
        """Get memory information"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            return {
                'total_gb': memory.total / (1024**3),
                'available_gb': memory.available / (1024**3),
                'used_gb': memory.used / (1024**3),
                'percent': memory.percent
            }
        except ImportError:
            return {
                'total_gb': None,
                'available_gb': None,
                'used_gb': None,
                'percent': None
            }
    
    def _get_gpu_info(self):
        """Get GPU information"""
        try:
            result = subprocess.run(['nvidia-smi'], 
                                  capture_output=True, 
                                  text=True, 
                                  encoding='utf-8')
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                gpu_info = "NVIDIA GPU detected"
                
                # Parse GPU model
                for line in lines:
                    if "NVIDIA GeForce RTX" in line or "NVIDIA RTX" in line:
                        gpu_parts = line.strip().split()
                        for i, part in enumerate(gpu_parts):
                            if "RTX" in part and i+1 < len(gpu_parts):
                                gpu_info = f"NVIDIA GeForce {part} {gpu_parts[i+1]}"
                                break
                        break
                
                return {
                    'detected': True,
                    'info': gpu_info,
                    'nvidia': True
                }
            else:
                return {
                    'detected': False,
                    'info': "No NVIDIA GPU detected",
                    'nvidia': False
                }
        except FileNotFoundError:
            return {
                'detected': False,
                'info': "NVIDIA GPU drivers not found",
                'nvidia': False
            }
        except Exception:
            return {
                'detected': False,
                'info': "Could not detect GPU",
                'nvidia': False
            }
    
    def _clean_gpu_info(self, gpu_info):
        """Clean and format GPU information"""
        clean_gpu_info = gpu_info.replace("NVIDIA GeForce", "").strip()
        
        if "NVIDIA GeForce RTX" in clean_gpu_info:
            parts = clean_gpu_info.split()
            for i, part in enumerate(parts):
                if part.isdigit() and i > 0 and "RTX" in parts[i-1]:
                    clean_gpu_info = f"NVIDIA GeForce RTX {part}"
                    break
        
        if not clean_gpu_info or clean_gpu_info.isspace():
            clean_gpu_info = "NVIDIA GeForce RTX 3060"
        
        return clean_gpu_info
    
    def _get_ai_recommendations(self, gpu_info):
        """Get AI model recommendations based on hardware"""
        clean_gpu_info = self._clean_gpu_info(gpu_info['info'])
        
        if "RTX" in clean_gpu_info:
            rec_text = "✓ Your system has an NVIDIA RTX GPU, which is excellent for running AI models.\n"
            rec_text += "✓ You can run most 7B and 13B models efficiently.\n"
            if "3060" in clean_gpu_info:
                rec_text += "✓ With 12GB VRAM, you can run models up to 13B parameters comfortably."
            elif "4090" in clean_gpu_info or "4080" in clean_gpu_info:
                rec_text += "✓ With your high-end GPU, you can run even 70B models efficiently."
            else:
                rec_text += "✓ Check your GPU's VRAM for optimal model size selection."
        else:
            rec_text = "⚠ No NVIDIA GPU detected. AI models will run slowly on CPU.\n"
            rec_text += "⚠ Consider using smaller models (2B-7B) for better performance.\n"
            rec_text += "⚠ For best performance, consider getting an NVIDIA RTX GPU."
        
        return rec_text
    
    def show_system_info(self):
        """Display system information in a GUI dialog"""
        if self.status_callback:
            self.status_callback("Gathering system information...")
        
        # Get system information
        sys_info = self.get_system_info()
        
        # Create dialog
        sys_info_dialog = tk.Toplevel(self.root)
        sys_info_dialog.title("System Information")
        sys_info_dialog.geometry("650x600")
        sys_info_dialog.resizable(False, False)
        sys_info_dialog.transient(self.root)
        sys_info_dialog.grab_set()
        
        # Try to set icon
        try:
            sys_info_dialog.iconbitmap("icon.ico")
        except:
            pass
        
        # Create main frame
        main_frame = ttk.Frame(sys_info_dialog, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="System Information", 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 15))
        
        # Hardware & Software Details
        info_frame = ttk.LabelFrame(main_frame, text="Hardware & Software Details", 
                                   padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Add system information rows
        self._add_info_row(info_frame, "🖥️", f"System: {sys_info['system']} {sys_info['release']}")
        self._add_info_row(info_frame, "🐍", f"Python: {sys_info['python_version']}")
        self._add_info_row(info_frame, "⚙️", f"Processor: {sys_info['processor']}")
        
        # Memory information
        if sys_info['memory']['total_gb']:
            memory_text = f"Memory: {sys_info['memory']['total_gb']:.1f} GB total, "
            memory_text += f"{sys_info['memory']['available_gb']:.1f} GB available "
            memory_text += f"({sys_info['memory']['percent']:.1f}% used)"
            self._add_info_row(info_frame, "💾", memory_text)
        else:
            self._add_info_row(info_frame, "💾", "Memory: Information not available")
        
        # GPU information
        clean_gpu_info = self._clean_gpu_info(sys_info['gpu']['info'])
        self._add_info_row(info_frame, "🎮", f"Graphics: {clean_gpu_info}")
        
        # AI Recommendations
        rec_frame = ttk.LabelFrame(main_frame, text="AI Model Recommendations", 
                                  padding=10)
        rec_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        rec_text = self._get_ai_recommendations(sys_info['gpu'])
        rec_label = ttk.Label(rec_frame, text=rec_text, font=("Arial", 9), 
                             justify=tk.LEFT, wraplength=580)
        rec_label.pack(anchor=tk.W)
        
        # Close button
        close_btn = ttk.Button(main_frame, text="Close", 
                             command=sys_info_dialog.destroy, width=15)
        close_btn.pack(pady=(15, 0))
        
        if self.status_callback:
            self.status_callback("System information displayed")
    
    def _add_info_row(self, parent, icon, text):
        """Add an information row with icon and text"""
        row_frame = ttk.Frame(parent)
        row_frame.pack(fill=tk.X, pady=5)
        
        icon_label = ttk.Label(row_frame, text=icon, font=("Arial", 12), 
                              width=2, anchor="w")
        icon_label.pack(side=tk.LEFT, padx=(0, 10))
        
        text_label = ttk.Label(row_frame, text=text, font=("Arial", 10))
        text_label.pack(side=tk.LEFT)
    
    @staticmethod
    def get_quick_info():
        """Get quick system information summary"""
        info_getter = SystemInfo()
        sys_info = info_getter.get_system_info()
        
        return {
            'system': f"{sys_info['system']} {sys_info['release']}",
            'python': sys_info['python_version'],
            'memory_gb': sys_info['memory']['total_gb'] if sys_info['memory']['total_gb'] else 0,
            'gpu_detected': sys_info['gpu']['detected'],
            'nvidia_gpu': sys_info['gpu']['nvidia']
        }


def main():
    """Main function for testing the module"""
    info_getter = SystemInfo()
    
    print("Getting system information...")
    quick_info = info_getter.get_quick_info()
    
    print(f"System: {quick_info['system']}")
    print(f"Python: {quick_info['python']}")
    print(f"Memory: {quick_info['memory_gb']:.1f} GB")
    print(f"GPU detected: {quick_info['gpu_detected']}")
    print(f"NVIDIA GPU: {quick_info['nvidia_gpu']}")


if __name__ == "__main__":
    main()
